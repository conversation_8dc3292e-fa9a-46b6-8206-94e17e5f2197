name: "\U0001F41B Bug / Help"
description: Create a report to help us improve the LLaMA Factory
body:
  - type: checkboxes
    id: reminder
    attributes:
      label: Reminder
      description: |
        Please ensure you have read the README carefully and searched the existing issues.
        请确保您已经认真阅读了 README 并且搜索过现有的 Issue。

      options:
        - label: I have read the README and searched the existing issues.
          required: true

  - type: textarea
    id: reproduction
    validations:
      required: true
    attributes:
      label: Reproduction
      description: |
        Please provide code snippets, error messages and stack traces that reproduces the problem.
        请提供运行参数，错误信息以及异常堆栈以便于我们复现该问题。
        Remember to use Markdown tags to correctly format your code.
        请合理使用 Markdown 标签来格式化您的文本。

      placeholder: |
        python src/train_bash.py ...

  - type: textarea
    id: expected-behavior
    validations:
      required: false
    attributes:
      label: Expected behavior
      description: |
        Please provide a clear and concise description of what you would expect to happen.
        请提供您原本的目的，即这段代码的期望行为。

  - type: textarea
    id: system-info
    validations:
      required: false
    attributes:
      label: System Info
      description: |
        Please share your system info with us. You can run the command **transformers-cli env** and copy-paste its output below.
        请提供您的系统信息。您可以在命令行运行 **transformers-cli env** 并将其输出复制到该文本框中。

      placeholder: transformers version, platform, python version, ...

  - type: textarea
    id: others
    validations:
      required: false
    attributes:
      label: Others
