{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["使用notebook运行TunningFactory的例子。\n", "0. 在\"星云开发工作台\"创建notebook，然后下载TunningFacoty，在jupyterLab页面点击：clone按钮进行下载\n", "代码下载路径：examples/TuningFactory\n", "试用vscode web ide open路径：/home/<USER>/notebook/examples/TuningFactory/\n", "1. 运行如下命令安装依赖包"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "!{ sys.executable } -m pip install -r requirements.txt --trusted-host mirrors.aliyun.com  -i http://mirrors.aliyun.com/pypi/simple/"]}, {"cell_type": "markdown", "metadata": {}, "source": ["1.1 如果安装openlm-hub失败，运行如下命令，如果安装成功，跳过该步骤。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "!{ sys.executable } -m pip install -i http://yum.tbsite.net/aliyun-pypi/simple/ --trusted-host=yum.tbsite.net openlm-hub  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["2. 点击'Restart'按钮重启kernel\n", "3. 重启完成后，运行如下命令，试用Qwen-VL-Chat模型进行generate:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "!{ sys.executable } src/generate.py --model_name_or_path Qwen/Qwen-VL-Chat --batch_size 8 --template qwen-vl --temperature 1.0 --top_p 0.8 --top_k 100 --seed 1 --cutoff_len 1024 --max_new_tokens 512 --infer_mode default --inputs odps://nebula_ai_dev/tables/ads_finetune/ds=qwenvl_900 --outputs odps://nebula_ai_dev/tables/llm_generate_output/ds=lzd_test_0307 --prompt_column 0 --flash_attn=False --tables odps://nebula_ai_dev/tables/ads_finetune/ds=qwenvl_900"]}, {"cell_type": "markdown", "metadata": {}, "source": ["上面的例子展示了批量生成的使用方式，也可以使用如下的例子进行交互式的模型体验，交互式的例子只是为了方便模型体验，大批量的离线生成和sft，就还是参考上面的使用方式\n", "4. 下面代码来自于[Qwen-VL-Chat在openlm的model card](https://openlm.alibaba-inc.com/web/preTrain/detail?id=152)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import AutoModelForCausalLM, AutoTokenizer\n", "from transformers.generation import GenerationConfig\n", "import torch\n", "torch.manual_seed(1234)\n", "\n", "# Note: The default behavior now has injection attack prevention off.\n", "tokenizer = AutoTokenizer.from_pretrained(\"Qwen/Qwen-VL-Chat\", trust_remote_code=True)\n", "\n", "# use bf16\n", "# model = AutoModelForCausalLM.from_pretrained(\"Qwen/Qwen-VL-Chat\", device_map=\"auto\", trust_remote_code=True, bf16=True).eval()\n", "# use fp16\n", "# model = AutoModelForCausalLM.from_pretrained(\"Qwen/Qwen-VL-Chat\", device_map=\"auto\", trust_remote_code=True, fp16=True).eval()\n", "# use cpu only\n", "# model = AutoModelForCausalLM.from_pretrained(\"Qwen/Qwen-VL-Chat\", device_map=\"cpu\", trust_remote_code=True).eval()\n", "# use cuda device\n", "model = AutoModelForCausalLM.from_pretrained(\"Qwen/Qwen-VL-Chat\", device_map=\"cuda\", trust_remote_code=True).eval()\n", "\n", "# Specify hyperparameters for generation (No need to do this if you are using transformers>=4.32.0)\n", "# model.generation_config = GenerationConfig.from_pretrained(\"Qwen/Qwen-VL-Chat\", trust_remote_code=True)\n", "\n", "# 1st dialogue turn\n", "query = tokenizer.from_list_format([\n", "    {'image': 'https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen-VL/assets/demo.jpeg'},\n", "    {'text': '这是什么'},\n", "])\n", "response, history = model.chat(tokenizer, query=query, history=None)\n", "print(response)\n", "# 图中是一名年轻女子在沙滩上和她的狗玩耍，狗的品种可能是拉布拉多。她们坐在沙滩上，狗的前腿抬起来，似乎在和人类击掌。两人之间充满了信任和爱。\n", "\n", "# 2nd dialogue turn\n", "response, history = model.chat(tokenizer, '输出\"击掌\"的检测框', history=history)\n", "print(response)\n", "# <ref>击掌</ref><box>(517,508),(589,611)</box>\n", "image = tokenizer.draw_bbox_on_latest_picture(response, history)\n", "if image:\n", "  image.save('1.jpg')\n", "else:\n", "  print(\"no box\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python3.10", "language": "python", "name": "python3.10"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}