#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版：移除字符串中 <think> 标签及其内容
"""

import re


def remove_think_tags(text: str) -> str:
    """
    移除字符串中所有的 <think> 标签及其内容
    
    Args:
        text: 包含 <think> 标签的字符串
        
    Returns:
        移除 <think> 标签后的字符串
    """
    # 使用正则表达式匹配 <think>...</think> 标签及其内容
    # .*? 表示非贪婪匹配，re.DOTALL 让 . 匹配换行符
    pattern = r'<think>.*?</think>'
    result = re.sub(pattern, '', text, flags=re.DOTALL)
    
    # 清理多余的空行
    result = re.sub(r'\n\s*\n+', '\n\n', result)
    
    return result.strip()


# 使用示例
if __name__ == "__main__":
    # 测试文本
    test_text = """这是正常内容。

<think>
这是思考过程，需要被移除
可能包含多行
</think>

这是保留的内容。

<think>另一个思考块</think>

最后的内容。"""

    print("原始文本:")
    print(test_text)
    print("\n" + "="*50)
    print("移除 <think> 标签后:")
    print(remove_think_tags(test_text))
    
    # 一行代码的简单版本
    print("\n" + "="*50)
    print("一行代码实现:")
    simple_result = re.sub(r'<think>.*?</think>', '', test_text, flags=re.DOTALL).strip()
    print(simple_result)
