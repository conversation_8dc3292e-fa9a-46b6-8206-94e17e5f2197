#!/bin/bash
set +x

workdir=$(cd $(dirname $0); pwd)
source $workdir/config.sh

WORLD_SIZE=32
LR=4e-5


VERSION=$(date "+%Y-%m-%d-%H-%M-%S")
PARAMETER=8B
# pretrain产出模型信息
#SAVE_MODEL="taobao_dev_mtl.chenniu-codegen-7b-09-pt/version=v3"
SAVE_MODEL="taobao_dev_mtl.chenniu-MTL-$PARAMETER/version=$VERSION"

# 要加载的base模型
#MODEL_NAME="Qwen/Qwen2.5-Coder-7B-Instruct"
#MODEL_NAME="/data/xpfs_0/Qwen2.5_72B_Base/model/" # docstring的余弦退火阶段1
MODEL_NAME="/data/xpfs_0/common/models/Qwen/Qwen3-4B/" # docstring的余弦退火阶段1
#MODEL_NAME="Qwen/Qwen2.5-Coder-$PARAMETER" # docstring的余   弦退火阶段1
#MODEL_NAME="Qwen/CodeQwen1.5-$PARAMETER" # docstring的余弦退火阶段1
#MODEL_NAME='dfs://ea119dfssearch7--cn-shanghai/nebula/model/taobao_dev_mtl/chenniu-codeCompletion-7B/2025-03-02-01-20-46_edc5d563ceb7/checkpoint-10566'

#DATASET="pt-c-fim-1,pt-c-fim-2,pt-java-fim-1,pt-java-fim-2,pt-java-fim-3,pt-java-fim-4,pt-java-fim-5,pt-swift-fim-1,pt-swift-fim-2,pt-swift-fim-3,pt-swift-fim-4,pt-swift-fim-5" # sft-block-fim-blank-etherll
#DATASET+=",pt-taotian-fim2,pt-taotian-fim1,pt-kotlin-fim"
DATASET="all-0707"
#DATASET="pt-merged-fim11-2"
#DATASET+=",pt-merged-fim-fixed" # 准备断点续训用的
DATASET+=",pt-wiki_zh"
#DATASET+=',sft-mtl-instruction,sft-identity,sft-shoutao-QA'
#DATASET="pt-pay-harmony-c"
#DATASET+="pt-pay-ios,pt-pay-android,pt-pay-harmony-ets,pt-pay-harmony-c" # 支付宝的终端代码数据，已经清洗
#DATASET+=",pt-MTL-code-0" # 摩天轮的终端代码数据，已经清洗，合并成同一个文件
#DATASET+=",pt-random-fim,pt-random-fim-keyword,pt-block-fim-sft"
#DATASET+=",pt-ast-fim" # 测试了一下 发现加上也没有更牛

#--dataset pt-wiki_zh,pt-MTL-code-0,pt-MTL-code-1,pt-MTL-code-2,pt-shoutao-code,pt-taobao-iOS,pt-taobao-Android,pt-shoutao,pt-stack-swift,pt-pay-mini,pt-pay-ios,pt-pay-android,pt-pay-harmony-ets,pt-pay-harmony-c \

JOB_NAME="$MODEL_NAME-$VERSION"


args="--stage pt \
    --model_name_or_path=$MODEL_NAME \
    --do_train \
    --do_eval  \
    --val_size=0.005 \
    --eval_steps 50 \
    --dataset=$DATASET\
    --prompt=text \
    --template default \
    --finetuning_type full \
    --output_dir=/data/xpfs_0/4B_base_0707/ \
    --overwrite_cache \
    --per_device_eval_batch_size 1 \
    --per_device_train_batch_size 2 \
    --gradient_accumulation_steps 2 \
    --lr_scheduler_type cosine \
    --logging_steps 1 \
    --save_steps 200 \
    --num_train_epochs 2 \
    --learning_rate=$LR \
    --warmup_ratio 0.1 \
    --cutoff_len=8192 \
    --packing False \
    --preprocessing_num_workers=64 \
    --dataloader_num_workers=4 \
    --plot_loss \
    --do_sample \
    --deepspeed=scripts/ds_zero3.json \
    --bf16"

mdl_args="--queue=${QUEUE} \
        --entry="src/train_bash.py" \
        --worker_count=${WORLD_SIZE}  \
        --file.cluster_file=scripts/cluster.json \
        --oss_access_id=${OSS_ACCESS_ID} \
        --oss_access_key=${OSS_ACCESS_KEY} \
        --oss_bucket=${OSS_BUCKET} \
        --job_name=${JOB_NAME} \
        --force \
        --job_success_notice=true \
        --oss_endpoint=${OSS_ENDPOINT} \
        --algo_name=pytorch240"

if [ -n "${OPENLM_TOKEN}" ]; then
    mdl_args="${mdl_args} --env=OPENLM_TOKEN=${OPENLM_TOKEN}"
fi

if [ -n "${ODPS_PROJECT}" ]; then
    mdl_args="${mdl_args} --tables=${ODPS_TABLE} --odps_project=${ODPS_PROJECT}"
fi

if [ -n "${TRACKER_PROJECT_NAME}" ]; then
    mdl_args="${mdl_args} --tracker_project_name=${TRACKER_PROJECT_NAME}"
fi

nebulactl run mdl --user_params="${args}"  $mdl_args
