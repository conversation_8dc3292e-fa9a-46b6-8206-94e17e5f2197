# 模型复制与提交指南
## 概述
此指南介绍如何使用 
`ckpt_copyfile.py` 复制模型，并通过 `submit.sh` 提交操作。
这些步骤对于确保模型的正确复制和提交至关重要。
## 执行步骤
在训练完成模型后，需要执行对应的 `submit.sh` 脚本。这是为了避免可能出现的模型覆盖问题。
### 执行 `submit.sh`
1. 切换到 `scripts` 目录：
```bash
cd scripts
```
2. 运行 `submit.sh`：
```bash
sh submit.sh
   ```
## 三个执行脚本介绍
您需要根据具体情况选择执行以下脚本之一：
1. `pt4coder-t4.sh`
2. `sft4coder72b-16-dp.sh`
3. `sft4coder72b-32-dp.sh`
4. `sft4coder72b-32-dp_fix.sh` # 快速修复问题
## 脚本执行命令
以下是如何执行这些脚本的命令示例：
```bash
bash script/sft4coder72b-32-dp.sh
```
通过上述步骤，确保模型正确复制和提交。如果对任何脚本有疑问，请仔细检查脚本内容与相关环境配置。