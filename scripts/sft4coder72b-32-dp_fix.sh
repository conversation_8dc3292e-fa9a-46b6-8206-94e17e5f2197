#!/bin/bash
set +x

workdir=$(cd $(dirname $0); pwd)
source $workdir/config.sh

WORLD_SIZE=128
LR=1e-5
VERSION=$(date "+%Y-%m-%d-%H-%M-%S")
SAVE_MODEL="taobao_dev_mtl.chenniu-ds/version=$VERSION"
#SAVE_MODEL="taobao_dev_mtl.chenniu-codegen25-codeEditor/version=v0"

PROMPT_TEMPLATE="qwen3"

#MODEL_NAME="dfs://ea119dfssearch7--cn-shanghai/nebula/model/taobao_dev_mtl/chenniu-MTL-7B/2025-04-08-21-15-47_bf2fc13f8fa4/checkpoint-1365"
#MODEL_NAME="/data/xpfs_0/PT_MODEL_04_08/PT_MODEL_04_08/checkpoint-1365/"
#MODEL_NAME="/data/xpfs_0/72b_base/checkpoint-910/"
#MODEL_NAME="/data/xpfs_0/ckpt_72B_32k_0428/checkpoint-67/"

#MODEL_NAME="/data/xpfs_0/ckpt_72B_32k_0423/checkpoint-53/"
MODEL_NAME="/data/xpfs_0/data/model/AIMI-32B-0522/"

# 数据集分区
DATASET="cot-q3-self,cot-q3-16k,cot-q3-8k,q3-multi-turn"
#DATASET="sft-identity,sft-identity"
echo $DATASET
JOB_NAME="EditorV2bs8cut8K-$VERSION"

args="--stage sft \
    --model_name_or_path=$MODEL_NAME \
    --do_train \
    --do_eval  \
    --val_size=0.005 \
    --eval_steps 50 \
    --dataset=$DATASET\
    --prompt=instruction \
    --query=input \
    --response=output \
    --template=${PROMPT_TEMPLATE} \
    --finetuning_type full \
    --output_dir=/data/xpfs_0/data/model/AIMI-32B-0603-FIX/ \
    --overwrite_cache \
    --per_device_train_batch_size 1 \
    --per_device_eval_batch_size 1 \
    --gradient_accumulation_steps 4 \
    --lr_scheduler_type cosine \
    --logging_steps 1 \
    --save_steps 500 \
    --num_train_epochs 1 \
    --learning_rate=$LR \
    --packing False \
    --cutoff_len=16384 \
    --preprocessing_num_workers=8 \
    --warmup_ratio 0.1 \
    --dataloader_num_workers=4 \
    --plot_loss \
    --deepspeed=scripts/ds_zero3.json \
    --bf16"

mdl_args="--queue=${QUEUE} \
        --entry="src/train_bash.py" \
        --worker_count=${WORLD_SIZE}  \
        --file.cluster_file=scripts/cluster.json \
        --oss_access_id=${OSS_ACCESS_ID} \
        --oss_access_key=${OSS_ACCESS_KEY} \
        --oss_bucket=${OSS_BUCKET} \
        --oss_endpoint=${OSS_ENDPOINT} \
        --force \
        --job_name=${JOB_NAME} \
        --job_success_notice=true \
        --algo_name=pytorch220"


if [ -n "${ODPS_PROJECT}" ]; then
    mdl_args="${mdl_args} --tables=${ODPS_TABLE} --odps_project=${ODPS_PROJECT}"
fi

if [ -n "${TRACKER_PROJECT_NAME}" ]; then
    mdl_args="${mdl_args} --tracker_project_name=${TRACKER_PROJECT_NAME}"
fi

export DS_SKIP_CUDA_CHECK=1
nebulactl run mdl --user_params="${args}"  $mdl_args