#!/bin/bash
set +x

workdir=$(cd $(dirname $0); pwd)
source $workdir/config.sh

PROMPT_TEMPLATE=""
# mos uri / huggingface repo-id of finetune model
MODEL_NAME=""

# 产出的Model信息
SAVE_MODEL="your_nebula_project.your_nebula_model/version=your_version"

# local_file或者oss挂载文件
INPUT="data/alpaca_kto_en_demo.json"

# odps table
# ODPS_PROJECT="your_odps_project"
# INPUT="odps://your_odps_project/tables/your_odps_table/ds=xxx"

WORLD_SIZE=8
LR=1e-5

# 实验项目名，需要在星云实验管理中创建实验或者选择你有权限的实验
# 如果不指定，并且开启了report_to=ml_tracker,会默认使用 你工号_default 作为实验项目名
#TRACKER_PROJECT_NAME=""
JOB_NAME="your_job_name"


args="--stage kto \
    --model_name_or_path=$MODEL_NAME \
    --do_train \
    --file_name=${INPUT} \
    --prompt=instruction \
    --response=output \
    --kto_tag=label \
    --template=$PROMPT_TEMPLATE \
    --finetuning_type full \
    --output_dir=local/tmp/ckpt_save_path/ \
    --overwrite_cache \
    --per_device_train_batch_size 1 \
    --gradient_accumulation_steps 4 \
    --lr_scheduler_type cosine \
    --logging_steps 1 \
    --save_steps 100 \
    --max_steps 100 \
    --learning_rate=$LR \
    --cutoff_len=1024 \
    --preprocessing_num_workers=8 \
    --dataloader_num_workers=4 \
    --plot_loss \
    --report_to=ml_tracker \
    --deepspeed=scripts/ds_zero2.json \
    --bf16"


mdl_args="--queue=${QUEUE} \
        --entry="src/train_bash.py" \
        --worker_count=${WORLD_SIZE}  \
        --file.cluster_file=scripts/cluster.json \
        --oss_access_id=${OSS_ACCESS_ID} \
        --oss_access_key=${OSS_ACCESS_KEY} \
        --oss_bucket=${OSS_BUCKET} \
        --oss_endpoint=${OSS_ENDPOINT} \
        --job_name=${JOB_NAME} \
        --_NEBULA_MODEL=${SAVE_MODEL} \
        --nebula_model=${SAVE_MODEL} \
        --algo_name=pytorch220"

if [ -n "${OPENLM_TOKEN}" ]; then
    mdl_args="${mdl_args} --env=OPENLM_TOKEN=${OPENLM_TOKEN}"
fi

if [ -n "${ODPS_PROJECT}" ]; then
    mdl_args="${mdl_args} --tables=${INPUT} --odps_project=${ODPS_PROJECT}"
fi

if [ -n "${TRACKER_PROJECT_NAME}" ]; then
    mdl_args="${mdl_args} --tracker_project_name=${TRACKER_PROJECT_NAME}"
fi

nebulactl run mdl --user_params="${args}" --ignore=venv/*  $mdl_args
