#!/bin/bash
set +x

workdir=$(cd $(dirname $0); pwd)
source $workdir/config.sh

WORLD_SIZE=1

# merge后保存的MOS路径，当保存到OSS/NAS时请注释本行
SAVE_MODEL="your_nebula_project.your_nebula_model/version=your_version"

# mos uri / path of turbo ckpt
MODEL_NAME=""

# merge 后的模型保存路径（SAVE_MODEL 未配置 MOS 路径时，请配置挂载OSS/NAS路径）
EXPORT_DIR="local/tmp/ckpt_save_path/"


args="--model_name_or_path=$MODEL_NAME \
    --export_dir=$EXPORT_DIR \
    --bf16"

mdl_args="--queue=${QUEUE} \
        --entry=src/convert_turbo.py \
        --worker_count=${WORLD_SIZE}  \
        --file.cluster_file=scripts/cluster.json \
        --oss_access_id=${OSS_ACCESS_ID} \
        --oss_access_key=${OSS_ACCESS_KEY} \
        --oss_bucket=${OSS_BUCKET} \
        --oss_endpoint=${OSS_ENDPOINT} \
        --algo_name=pytorch220"

if [ -n "${SAVE_MODEL}" ]; then
    mdl_args="${mdl_args} --_NEBULA_MODEL=${SAVE_MODEL} --nebula_model=${SAVE_MODEL}"
fi

nebulactl run mdl --user_params="${args}"  $mdl_args
