#!/bin/bash
set +x

workdir=$(cd $(dirname $0); pwd)
source $workdir/config.sh

# 蒸馏产出的Model信息
SAVE_MODEL="your_nebula_project.your_nebula_model/version=your_version"
# local_file或者oss挂载文件
INPUT="data/alpaca_gpt4_data_zh.json"
# odps table
# ODPS_PROJECT="your_odps_project"
# INPUT="odps://your_odps_project/tables/your_odps_table/ds=xxx"

#  PROMPT_TEMPLATE: 不同的模型有不同的 `prompt` 构造方式，仓库提供了丰富的 `prompt` 构造 `template`
#  全部可用 `template` 可见 [src/llmtuner/extras/template.py](src/llmtuner/extras/template.py)， 
#  建议使用模型对应的 `template` 进行SFT和推理。 常规模型配置可见：https://aliyuque.antfin.com/uxctvg/yd0939/qzayp5pyyxd0e46h
PROMPT_TEMPLATE=""
MODEL_NAME=""
LORA_TARGET="o_proj,q_proj,k_proj,v_proj" # 具体可以查看各个model手册中的LoRAModule

TEACHER_PROMPT_TEMPLATE=""
TEACHER_MODEL_NAME=""
# if you want to train based on lora ckpt, please set LORA_CKPT and set MODEL_NAME to pretrained model
# TEACHER_LORA_CKPT=""
# LORA_CKPT=""

WORLD_SIZE=8
LR=2e-5
BATCH_SIZE=2
GRAD_ACC_STEPS=2
# 实验项目名，需要在星云实验管理中创建实验或者选择你有权限的实验
# 如果不指定，并且开启了report_to=ml_tracker,会默认使用 你工号_default 作为实验项目名
# TRACKER_PROJECT_NAME=""
JOB_NAME="your_job_name"

# 必须检查step
args="--stage distill \
    --distill_loss_weight=0.85 \
    --distill_on_prompt=True \
    --kd_objective=forward_kl \
    --finetuning_type=lora \
    --lora_target=${LORA_TARGET}  \
    --lora_rank=8 \
    --model_name_or_path=$MODEL_NAME \
    --template=${PROMPT_TEMPLATE} \
    --teacher_model_name_or_path=$TEACHER_MODEL_NAME \
    --teacher_template=${TEACHER_PROMPT_TEMPLATE} \
    --do_train \
    --file_name=${INPUT} \
    --prompt=prompt \
    --response=response \
    --output_dir=local/tmp/ckpt_save_path/ \
    --overwrite_cache \
    --per_device_train_batch_size=${BATCH_SIZE} \
    --gradient_accumulation_steps=${GRAD_ACC_STEPS} \
    --lr_scheduler_type cosine \
    --logging_steps 20 \
    --save_steps=100 \
    --max_steps=100 \
    --learning_rate=$LR \
    --cutoff_len=4096 \
    --bf16 \
    --deepspeed=scripts/ds_zero2.json \
    --learning_rate 2e-5 \
    --preprocessing_num_workers=8 \
    --dataloader_num_workers=4 \
    --report_to=ml_tracker \
    --plot_loss"


if [ -n "${LORA_CKPT}" ]; then
    args="${args} --adapter_name_or_path=${LORA_CKPT}"
fi

if [ -n "${TEACHER_LORA_CKPT}" ]; then
    args="${args} --teacher_adapter_name_or_path=${TEACHER_LORA_CKPT}"
fi

mdl_args="--queue=${QUEUE} \
        --entry="src/train_bash.py" \
        --worker_count=${WORLD_SIZE}  \
        --file.cluster_file=scripts/cluster.json \
        --oss_access_id=${OSS_ACCESS_ID} \
        --oss_access_key=${OSS_ACCESS_KEY} \
        --oss_bucket=${OSS_BUCKET} \
        --oss_endpoint=${OSS_ENDPOINT} \
        --_NEBULA_MODEL=${SAVE_MODEL} \
        --nebula_model=${SAVE_MODEL} \
        --job_name=${JOB_NAME}
        --algo_name=pytorch240"

if [ -n "${OPENLM_TOKEN}" ]; then
    mdl_args="${mdl_args} --env=OPENLM_TOKEN=${OPENLM_TOKEN}"
fi

if [ -n "${ODPS_PROJECT}" ]; then
    mdl_args="${mdl_args} --tables=${ODPS_TABLE} --odps_project=${ODPS_PROJECT}"
fi

if [ -n "${TRACKER_PROJECT_NAME}" ]; then
    mdl_args="${mdl_args} --tracker_project_name=${TRACKER_PROJECT_NAME}"
fi

nebulactl run mdl --user_params="${args}"  $mdl_args