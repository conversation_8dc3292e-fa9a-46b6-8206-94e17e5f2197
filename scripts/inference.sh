#!/bin/bash
set +x

workdir=$(cd $(dirname $0); pwd)
source $workdir/config.sh

WORLD_SIZE=1

PROMPT_TEMPLATE=""
# mos uri / huggingface repo-id of inference model
MODEL_NAME=""

# lora ckpt mos_uri, split by `,` eg: mos_uri1,mos_uri2
# LORA_CKPT=""

BATCH_SIZE=2

# INPUT&OUTPUT: local_file or oss挂载文件
INPUT="xxx.json"  # 可参考data/infer_input.json 样例
OUTPUT="/data/oss_bucket_0/xxx/glm3.json" # 请填 OSS 挂载地址，否则找不到输出文件

# INPUT&OUTPUT: odps table
# output表不存在时会自动构建output表，schema = input表列（可由INPUT_COLUMNS参数指定） + 输出列 （generate_results）
# 自建output表时请保证比input表列多一列，类型为string
#ODPS_PROJECT="your_odps_project"
#INPUT="odps://your_odps_project/tables/your_odps_table/ds=xxx"
#OUTPUT="odps://your_odps_project/tables/your_odps_table_for_infer_output/ds=xxx"

# json 文件中 prompt 列的名称。当以 ODPS 作为数据源时，配置 prompt 列的 column index，如第0列为id 第1列为prompt时，填 "1"
PROMPT_COLUMN="instruction"

# 用于指定选取输入表中哪些数据列保存到输出结果表，在odps表场景填写要读取列的索引，不填默认全部列；在oss file场景填写要读取的json key，不填默认全部json key；
# 配置方式：在args中添加 --input_columns=$INPUT_COLUMNS
# INPUT_COLUMNS=""

# 生成模式，默认default，default是huggingface提供的默认方式，vllm是vllm库提供的并行生成模式，vllm-async是vllm流式生成模式，vllm-multi-node是vllm跨机生成模式，适用于较大模型
GENERATE_MODE="default"

args="--model_name_or_path=$MODEL_NAME \
      --batch_size=$BATCH_SIZE \
      --template=$PROMPT_TEMPLATE \
      --load_from=file \
      --temperature=1.0 \
      --top_p=0.8 \
      --top_k=100 \
      --seed=1 \
      --cutoff_len=1024 \
      --max_new_tokens=512 \
      --infer_mode=$GENERATE_MODE \
      --inputs=$INPUT \
      --outputs=$OUTPUT \
      --prompt_column=$PROMPT_COLUMN"

if [ -n "${LORA_CKPT}" ]; then
    args="$args --adapter_name_or_path=$LORA_CKPT"
fi

mdl_args="--queue=${QUEUE} \
          --entry=src/generate.py \
          --worker_count=${WORLD_SIZE}  \
          --file.cluster_file=scripts/cluster.json \
          --oss_access_id=${OSS_ACCESS_ID} \
          --oss_access_key=${OSS_ACCESS_KEY} \
          --oss_bucket=${OSS_BUCKET} \
          --oss_endpoint=${OSS_ENDPOINT} \
          --algo_name=pytorch220"

if [[ $INPUT == "odps://"* ]]; then
    # max_failover_times含义参考EIP框架说明: https://aliyuque.antfin.com/uxctvg/gh8c24/lkr4c8#hI3Q6
    # 可根据实际情况进行调整设置
    mdl_args="$mdl_args --tables=${INPUT} --odps_project=${ODPS_PROJECT} --pytorch_async --max_failover_times=100"
fi

if [[ $INPUT == "ailake://"* ]]; then
    # max_failover_times含义参考EIP框架说明: https://aliyuque.antfin.com/uxctvg/gh8c24/lkr4c8#hI3Q6
    # 可根据实际情况进行调整设置
    mdl_args="$mdl_args --tables=${INPUT} --pytorch_async --max_failover_times=100"
fi

nebulactl run mdl --user_params="${args}"  $mdl_args
