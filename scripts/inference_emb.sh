#!/bin/bash
set +x

workdir=$(cd $(dirname $0); pwd)
source $workdir/config.sh

WORLD_SIZE=1

# mos uri / huggingface repo-id of inference model
MODEL_NAME=""

# 01-ai/Yi, llama2, not suppprt batch inference, batch_size should == 1
BATCH_SIZE=2

# INPUT&OUTPUT: local_file or oss挂载文件
INPUT="xxx.json"  # 可参考data/infer_input.json 样例
OUTPUT="/data/oss_bucket_0/xxx/glm3.json" # 请填 OSS 挂载地址，否则找不到输出文件

# INPUT&OUTPUT: odps table
# output表不存在时会自动构建output表，schema = input表列（可由INPUT_COLUMNS参数指定） + 输出列 （generate_results）
# 自建output表时请保证比input表列多一列，类型为string
#ODPS_PROJECT="your_odps_project"
#INPUT="odps://your_odps_project/tables/your_odps_table/ds=xxx"
#OUTPUT="odps://your_odps_project/tables/your_odps_table_for_infer_output/ds=xxx"

# json 文件中 prompt 列的名称。当以 ODPS 作为数据源时，配置 prompt 列的 column index，如第0列为id 第1列为prompt时，填 "1"
PROMPT_COLUMN="instruction"

# field_mapping, json格式, odps col_name to dataset col_name
# eg: '[{\"question\":\"question\",\"id\":\"id\"}]'
args="--model_name_or_path=$MODEL_NAME \
      --batch_size=$BATCH_SIZE \
      --template=empty \
      --seed=1 \
      --cutoff_len=4096 \
      --compute_dtype=torch.float32 \
      --flash_attn=auto \
      --infer_mode=default \
      --inputs=$INPUT \
      --outputs=$OUTPUT \
      --prompt_column=$PROMPT_COLUMN"

if [ -n "${LORA_CKPT}" ]; then
    args="$args --adapter_name_or_path=$LORA_CKPT"
fi

mdl_args="--queue=${QUEUE} \
          --entry=src/generate.py \
          --worker_count=${WORLD_SIZE}  \
          --file.cluster_file=scripts/cluster.json \
          --oss_access_id=${OSS_ACCESS_ID} \
          --oss_access_key=${OSS_ACCESS_KEY} \
          --oss_bucket=${OSS_BUCKET} \
          --oss_endpoint=${OSS_ENDPOINT} \
          --algo_name=pytorch240"

if [ -n "${OPENLM_TOKEN}" ]; then
    mdl_args="${mdl_args} --env=OPENLM_TOKEN=${OPENLM_TOKEN}"
fi

if [ -n "${ODPS_PROJECT}" ]; then
    # max_failover_times含义参考EIP框架说明: https://aliyuque.antfin.com/uxctvg/gh8c24/lkr4c8#hI3Q6
    # 可根据实际情况进行调整设置
    mdl_args="$mdl_args --tables=${INPUT} --odps_project=${ODPS_PROJECT} --pytorch_async --max_failover_times=100"
fi

nebulactl run mdl --user_params="${args}"  $mdl_args

