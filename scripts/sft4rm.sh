#!/bin/bash
set +x

workdir=$(cd $(dirname $0); pwd)
source $workdir/config.sh

WORLD_SIZE=8
LR=5e-5
VERSION=$(date "+%Y-%m-%d-%H-%M-%S")
SAVE_MODEL="taobao_dev_mtl.chenniu-ds/version=$VERSION"

PROMPT_TEMPLATE="deepseek-v3"

MODEL_NAME="deepseek-ai/DeepSeek-R1-Distill-Qwen-7B" # docstring的余弦退火阶段
# 数据集分区
DATASET+="sft-mtl-instruction,sft-identity,sft-shoutao-QA" # 身份认证数据集
DATASET+=",sft-codeRule-0324" # 代码规则匹配
echo $DATASET
JOB_NAME="EditorV2bs8cut8K-$VERSION"

args="--stage sft \
    --model_name_or_path=$MODEL_NAME \
    --do_train \
    --do_eval  \
    --val_size=0.005 \
    --eval_steps 50 \
    --dataset=$DATASET\
    --prompt=instruction \
    --query=input \
    --response=output \
    --template=${PROMPT_TEMPLATE} \
    --finetuning_type full \
    --lora_target=W_pack,o_proj,gate_proj,up_proj,down_proj \
    --output_dir=local/tmp/ckpt_save_path/ \
    --overwrite_cache \
    --per_device_train_batch_size 2 \
    --evaluation_strategy steps \
    --per_device_eval_batch_size 1 \
    --gradient_accumulation_steps 4 \
    --lr_scheduler_type cosine \
    --logging_steps 1 \
    --save_steps 500 \
    --num_train_epochs 1 \
    --learning_rate=$LR \
    --cutoff_len=16384 \
    --preprocessing_num_workers=8 \
    --warmup_ratio 0.1 \
    --dataloader_num_workers=4 \
    --plot_loss \
    --report_to=ml_tracker \
    --deepspeed=scripts/ds_zero3_cpuoffload.json \
    --bf16"

mdl_args="--queue=${QUEUE} \
        --entry="src/train_bash.py" \
        --worker_count=${WORLD_SIZE}  \
        --file.cluster_file=scripts/cluster.json \
        --oss_access_id=${OSS_ACCESS_ID} \
        --oss_access_key=${OSS_ACCESS_KEY} \
        --oss_bucket=${OSS_BUCKET} \
        --oss_endpoint=${OSS_ENDPOINT} \
        --force \
        --_NEBULA_MODEL=${SAVE_MODEL} \
        --nebula_model=${SAVE_MODEL} \
        --job_name=${JOB_NAME} \
        --job_success_notice=true \
        --algo_name=pytorch220"

if [ -n "${OPENLM_TOKEN}" ]; then
    mdl_args="${mdl_args} --env=OPENLM_TOKEN=${OPENLM_TOKEN}"
fi

if [ -n "${ODPS_PROJECT}" ]; then
    mdl_args="${mdl_args} --tables=${ODPS_TABLE} --odps_project=${ODPS_PROJECT}"
fi

if [ -n "${TRACKER_PROJECT_NAME}" ]; then
    mdl_args="${mdl_args} --tracker_project_name=${TRACKER_PROJECT_NAME}"
fi

export DS_SKIP_CUDA_CHECK=1
nebulactl run mdl --user_params="${args}"  $mdl_args