#!/bin/bash
# =============================================================================
# SFT Training Script for Reward Model Data Preparation
#
# 此脚本用于在Reward Model训练之前进行SFT训练，为后续的RM训练准备基础模型
# 主要用于训练代码编辑器相关的模型，包含身份认证和代码规则匹配数据
#
# 使用方法:
#   1. 直接运行: sh scripts/sft4rm.sh
#   2. 自定义参数: WORLD_SIZE=16 LR=3e-5 sh scripts/sft4rm.sh
#   3. 查看帮助: sh scripts/sft4rm.sh --help
#
# 可配置的环境变量:
#   WORLD_SIZE                    - GPU数量 (默认: 8)
#   LR                           - 学习率 (默认: 5e-5)
#   MODEL_NAME                   - 基础模型路径
#   PROMPT_TEMPLATE              - 提示模板 (默认: deepseek-v3)
#   BATCH_SIZE_PER_DEVICE        - 每设备批次大小 (默认: 2)
#   GRADIENT_ACCUMULATION_STEPS  - 梯度累积步数 (默认: 4)
#   NUM_TRAIN_EPOCHS             - 训练轮数 (默认: 1)
#   CUTOFF_LEN                   - 序列长度 (默认: 16384)
#   SAVE_STEPS                   - 保存步数 (默认: 500)
#   EVAL_STEPS                   - 评估步数 (默认: 50)
#   TRACKER_PROJECT_NAME         - 实验跟踪项目名
#
# 依赖文件:
#   - scripts/config.sh          - 基础配置文件
#   - scripts/ds_zero3_cpuoffload.json - DeepSpeed配置
#   - scripts/cluster.json       - 集群配置
#
# =============================================================================

set -e  # 遇到错误立即退出
set +x  # 不显示执行的命令

# 处理帮助参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "SFT Training Script for Reward Model Data Preparation"
    echo ""
    echo "用法:"
    echo "  sh scripts/sft4rm.sh [选项]"
    echo ""
    echo "选项:"
    echo "  --help, -h    显示此帮助信息"
    echo ""
    echo "环境变量配置:"
    echo "  WORLD_SIZE=8                     设置GPU数量"
    echo "  LR=5e-5                         设置学习率"
    echo "  MODEL_NAME=path/to/model        设置基础模型路径"
    echo "  BATCH_SIZE_PER_DEVICE=2         设置每设备批次大小"
    echo "  NUM_TRAIN_EPOCHS=1              设置训练轮数"
    echo "  CUTOFF_LEN=16384                设置序列长度"
    echo "  TRACKER_PROJECT_NAME=project    设置实验跟踪项目名"
    echo ""
    echo "示例:"
    echo "  # 使用默认配置"
    echo "  sh scripts/sft4rm.sh"
    echo ""
    echo "  # 自定义GPU数量和学习率"
    echo "  WORLD_SIZE=16 LR=3e-5 sh scripts/sft4rm.sh"
    echo ""
    echo "  # 使用自定义模型"
    echo "  MODEL_NAME=/path/to/custom/model sh scripts/sft4rm.sh"
    echo ""
    exit 0
fi

# 获取脚本所在目录并加载配置
workdir=$(cd $(dirname $0); pwd)
source $workdir/config.sh

# =============================================================================
# 训练配置参数
# =============================================================================

# 分布式训练配置
WORLD_SIZE=${WORLD_SIZE:-8}  # 默认8个GPU，可通过环境变量覆盖

# 学习率配置
LR=${LR:-5e-5}  # 默认学习率，可通过环境变量覆盖

# 版本和模型保存配置
VERSION=${VERSION:-$(date "+%Y-%m-%d-%H-%M-%S")}
SAVE_MODEL=${SAVE_MODEL:-"taobao_dev_mtl.chenniu-ds/version=$VERSION"}

# 模型和模板配置
PROMPT_TEMPLATE=${PROMPT_TEMPLATE:-"deepseek-v3"}
MODEL_NAME=${MODEL_NAME:-"deepseek-ai/DeepSeek-R1-Distill-Qwen-7B"}

# 数据集配置 - 为Reward Model训练准备的SFT数据
DATASET=""
DATASET+="sft-mtl-instruction,sft-identity,sft-shoutao-QA"  # 身份认证数据集
DATASET+=",sft-codeRule-0324"  # 代码规则匹配数据集

# 任务名称
JOB_NAME=${JOB_NAME:-"SFT4RM-EditorV2-$VERSION"}

# 输出数据集信息
echo "=============================================================================
训练配置信息:
- 模型: $MODEL_NAME
- 数据集: $DATASET
- 世界大小: $WORLD_SIZE
- 学习率: $LR
- 任务名称: $JOB_NAME
- 保存模型: $SAVE_MODEL
============================================================================="

# =============================================================================
# 参数验证
# =============================================================================

# 检查必要的环境变量
if [ -z "$QUEUE" ]; then
    echo "错误: QUEUE 环境变量未设置，请检查 config.sh"
    exit 1
fi

if [ -z "$OSS_ACCESS_ID" ] || [ -z "$OSS_ACCESS_KEY" ]; then
    echo "错误: OSS 访问凭证未设置，请检查 config.sh"
    exit 1
fi

# 检查DeepSpeed配置文件是否存在
DEEPSPEED_CONFIG="scripts/ds_zero3_cpuoffload.json"
if [ ! -f "$workdir/$DEEPSPEED_CONFIG" ]; then
    echo "错误: DeepSpeed配置文件不存在: $DEEPSPEED_CONFIG"
    exit 1
fi

# =============================================================================
# 训练参数配置
# =============================================================================

# 训练超参数配置
BATCH_SIZE_PER_DEVICE=${BATCH_SIZE_PER_DEVICE:-2}
EVAL_BATCH_SIZE_PER_DEVICE=${EVAL_BATCH_SIZE_PER_DEVICE:-1}
GRADIENT_ACCUMULATION_STEPS=${GRADIENT_ACCUMULATION_STEPS:-4}
NUM_TRAIN_EPOCHS=${NUM_TRAIN_EPOCHS:-1}
CUTOFF_LEN=${CUTOFF_LEN:-16384}
WARMUP_RATIO=${WARMUP_RATIO:-0.1}

# 评估和保存配置
VAL_SIZE=${VAL_SIZE:-0.005}
EVAL_STEPS=${EVAL_STEPS:-50}
SAVE_STEPS=${SAVE_STEPS:-500}
LOGGING_STEPS=${LOGGING_STEPS:-1}

# 数据处理配置
PREPROCESSING_NUM_WORKERS=${PREPROCESSING_NUM_WORKERS:-8}
DATALOADER_NUM_WORKERS=${DATALOADER_NUM_WORKERS:-4}

# 输出目录配置
OUTPUT_DIR=${OUTPUT_DIR:-"local/tmp/ckpt_save_path/"}

echo "训练超参数:
- 每设备训练批次大小: $BATCH_SIZE_PER_DEVICE
- 每设备评估批次大小: $EVAL_BATCH_SIZE_PER_DEVICE
- 梯度累积步数: $GRADIENT_ACCUMULATION_STEPS
- 训练轮数: $NUM_TRAIN_EPOCHS
- 序列长度: $CUTOFF_LEN
- 预热比例: $WARMUP_RATIO
- 验证集大小: $VAL_SIZE
============================================================================="

# 构建训练参数
args="--stage sft \
    --model_name_or_path=$MODEL_NAME \
    --do_train \
    --do_eval \
    --val_size=$VAL_SIZE \
    --eval_steps $EVAL_STEPS \
    --dataset=$DATASET \
    --prompt=instruction \
    --query=input \
    --response=output \
    --template=${PROMPT_TEMPLATE} \
    --finetuning_type full \
    --lora_target=W_pack,o_proj,gate_proj,up_proj,down_proj \
    --output_dir=$OUTPUT_DIR \
    --overwrite_cache \
    --per_device_train_batch_size $BATCH_SIZE_PER_DEVICE \
    --evaluation_strategy steps \
    --per_device_eval_batch_size $EVAL_BATCH_SIZE_PER_DEVICE \
    --gradient_accumulation_steps $GRADIENT_ACCUMULATION_STEPS \
    --lr_scheduler_type cosine \
    --logging_steps $LOGGING_STEPS \
    --save_steps $SAVE_STEPS \
    --num_train_epochs $NUM_TRAIN_EPOCHS \
    --learning_rate=$LR \
    --cutoff_len=$CUTOFF_LEN \
    --preprocessing_num_workers=$PREPROCESSING_NUM_WORKERS \
    --warmup_ratio $WARMUP_RATIO \
    --dataloader_num_workers=$DATALOADER_NUM_WORKERS \
    --plot_loss \
    --report_to=ml_tracker \
    --deepspeed=$DEEPSPEED_CONFIG \
    --bf16"

# =============================================================================
# 星云平台配置
# =============================================================================

# 检查cluster配置文件
CLUSTER_FILE="scripts/cluster.json"
if [ ! -f "$workdir/$CLUSTER_FILE" ]; then
    echo "错误: 集群配置文件不存在: $CLUSTER_FILE"
    exit 1
fi

# 构建MDL参数
mdl_args="--queue=${QUEUE} \
        --entry=src/train_bash.py \
        --worker_count=${WORLD_SIZE} \
        --file.cluster_file=$CLUSTER_FILE \
        --oss_access_id=${OSS_ACCESS_ID} \
        --oss_access_key=${OSS_ACCESS_KEY} \
        --oss_bucket=${OSS_BUCKET} \
        --oss_endpoint=${OSS_ENDPOINT} \
        --force \
        --_NEBULA_MODEL=${SAVE_MODEL} \
        --nebula_model=${SAVE_MODEL} \
        --job_name=${JOB_NAME} \
        --job_success_notice=true \
        --algo_name=pytorch220"

# 可选环境变量配置
if [ -n "${OPENLM_TOKEN}" ]; then
    mdl_args="${mdl_args} --env=OPENLM_TOKEN=${OPENLM_TOKEN}"
    echo "已配置 OPENLM_TOKEN"
fi

if [ -n "${ODPS_PROJECT}" ]; then
    if [ -z "${ODPS_TABLE}" ]; then
        echo "警告: ODPS_PROJECT 已设置但 ODPS_TABLE 未设置"
    else
        mdl_args="${mdl_args} --tables=${ODPS_TABLE} --odps_project=${ODPS_PROJECT}"
        echo "已配置 ODPS 数据源: ${ODPS_PROJECT}/${ODPS_TABLE}"
    fi
fi

if [ -n "${TRACKER_PROJECT_NAME}" ]; then
    mdl_args="${mdl_args} --tracker_project_name=${TRACKER_PROJECT_NAME}"
    echo "已配置实验跟踪项目: ${TRACKER_PROJECT_NAME}"
fi

# =============================================================================
# 执行训练
# =============================================================================

echo "
=============================================================================
开始执行SFT训练任务...
任务名称: $JOB_NAME
模型保存路径: $SAVE_MODEL
=============================================================================
"

# 设置DeepSpeed环境变量
export DS_SKIP_CUDA_CHECK=1

# 执行训练命令
echo "执行命令: nebulactl run mdl --user_params=\"${args}\" $mdl_args"
echo ""

if nebulactl run mdl --user_params="${args}" $mdl_args; then
    echo "
=============================================================================
✅ 训练任务提交成功！
任务名称: $JOB_NAME
模型保存路径: $SAVE_MODEL
请在星云平台查看训练进度和日志
============================================================================="
else
    echo "
=============================================================================
❌ 训练任务提交失败！
请检查配置参数和网络连接
============================================================================="
    exit 1
fi