#!/bin/bash
set +x

workdir=$(cd $(dirname $0); pwd)
source $workdir/config.sh

WORLD_SIZE=128
LR=2e-5
VERSION=$(date "+%Y-%m-%d-%H-%M-%S")
SAVE_MODEL="taobao_dev_mtl.chenniu-ds/version=$VERSION"
#SAVE_MODEL="taobao_dev_mtl.chenniu-codegen25-codeEditor/version=v0"

#PROMPT_TEMPLATE="qwen"
#PROMPT_TEMPLATE="deepseek-v3"
PROMPT_TEMPLATE="empty"
#MODEL_NAME="Qwen/Qwen2.5-Coder-7B"
MODEL_NAME="/data/xpfs_0/data/model/qwen_2.5_7B_0524_base_sft/"
#MODEL_NAME="deepseek-ai/DeepSeek-R1-Distill-Qwen-7B" # docstring的余弦退火阶段1
#MODEL_NAME="dfs://ea119dfssearch7--cn-shanghai/nebula/model/taobao_dev_mtl/chenniu-MTL-7B/2025-04-03-17-07-32_41830067d32e/checkpoint-4370"
#MODEL_NAME="dfs://ea119dfssearch7--cn-shanghai/nebula/model/taobao_dev_mtl/chenniu-ds/2025-04-06-13-30-01_9d33f4017ca9/checkpoint-3900"
#MODEL_NAME="deepseek-ai/DeepSeek-R1-Distill-Qwen-7B"
# 数据集分区
#DATASET="sft-synthia-coder,sft-hf-open-data,sft-inner-code-ut,sft-outer-review,sft-inner-review,sft-commit-pack-multi,sft-code-repair,sft-code-gen,sft-codefeedback-cn,sft-commit-pack,sft-evol-data,sft-codeFeed"
#DATASET+=",sft-evol-data-cn,sft-evol-data-cn-1,sft-shoutao,sft-alpaca_gpt4_zh,sft-codealpaca,sft-evol-code-gen"
#DATASET+=",sft-math-qa" # 数学推理数据集
#DATASET+="sft-mtl-instruction,sft-identity,sft-shoutao-QA" # 身份认证数据集
#DATASET+=",multi-function-call" # 多轮对话数据集
#DATASET+=",sft-opencoder-stage2" # OpenCoder-stage2SFT的数据集
#DATASET+=",sft-docstring_badcase,sft-docstring-v2,sft-docstring-upgrade,sft-swift-docstring,sft-java-docstring" # Docstring相关数据集
#DATASET="sft-codeRule-think-8k"
#DATASET="sft-codeRule-0313" # 代码规则匹配

DATASET="sft-inner-fim-0603"
#DATASET+=",cot-codeforce-8k,cot-codeforce-16k"
#DATASET+=",cot-coder-40k"
#DATASET+=",cot-fei-1k"
#DATASET+=",sft-coderuler-java,sft-coderuler-neg,sft-coderuler-android,sft-coderuler-bc,sft-bai-neg,sft-coderuler-1126" # ruler规则数据集
#DATASET+=",sft-coderuler-ios,sft-bai-train,sft-coderuler-bc2" # ruler规则数据集
echo $DATASET
JOB_NAME="EditorV2bs8cut8K-$VERSION"

args="--stage sft \
    --model_name_or_path=$MODEL_NAME \
    --do_train \
    --val_size=0.005 \
    --eval_steps 50 \
    --dataset=$DATASET\
    --prompt=instruction \
    --query=input \
    --response=output \
    --template=${PROMPT_TEMPLATE} \
    --finetuning_type full \
    --lora_target=W_pack,o_proj,gate_proj,up_proj,down_proj \
    --output_dir=local/tmp/ckpt_save_path/ \
    --overwrite_cache \
    --per_device_train_batch_size 4 \
    --per_device_eval_batch_size 1 \
    --gradient_accumulation_steps 2 \
    --lr_scheduler_type cosine \
    --logging_steps 1 \
    --save_steps 500 \
    --num_train_epochs 1 \
    --learning_rate=$LR \
    --packing False \
    --cutoff_len=8192 \
    --preprocessing_num_workers=8 \
    --warmup_ratio 0.1 \
    --dataloader_num_workers=4 \
    --plot_loss \
    --report_to=ml_tracker \
    --deepspeed=scripts/ds_zero3_cpuoffload.json \
    --bf16"

mdl_args="--queue=${QUEUE} \
        --entry="src/train_bash.py" \
        --worker_count=${WORLD_SIZE}  \
        --file.cluster_file=scripts/cluster.json \
        --oss_access_id=${OSS_ACCESS_ID} \
        --oss_access_key=${OSS_ACCESS_KEY} \
        --oss_bucket=${OSS_BUCKET} \
        --oss_endpoint=${OSS_ENDPOINT} \
        --force \
        --_NEBULA_MODEL=${SAVE_MODEL} \
        --nebula_model=${SAVE_MODEL} \
        --job_name=${JOB_NAME} \
        --job_success_notice=true \
        --algo_name=pytorch220"

if [ -n "${OPENLM_TOKEN}" ]; then
    mdl_args="${mdl_args} --env=OPENLM_TOKEN=${OPENLM_TOKEN}"
fi

if [ -n "${ODPS_PROJECT}" ]; then
    mdl_args="${mdl_args} --tables=${ODPS_TABLE} --odps_project=${ODPS_PROJECT}"
fi

if [ -n "${TRACKER_PROJECT_NAME}" ]; then
    mdl_args="${mdl_args} --tracker_project_name=${TRACKER_PROJECT_NAME}"
fi

export DS_SKIP_CUDA_CHECK=1
nebulactl run mdl --user_params="${args}"  $mdl_args