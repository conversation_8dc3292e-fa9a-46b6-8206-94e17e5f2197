#!/bin/bash
set +x

workdir=$(cd $(dirname $0); pwd)
source $workdir/config.sh

WORLD_SIZE=1

# prompt template
PROMPT_TEMPLATE=""

# mos_uri/oss_path of the reward model checkpoint
REWARD_CKPT=""

# 实验项目名，需要在星云实验管理中创建实验或者选择你有权限的实验
# 如果不指定，并且开启了report_to=ml_tracker,会默认使用 你工号_default 作为实验项目名
# TRACKER_PROJECT_NAME=""

# mos_uri/oss_path of the sft lora checkpoint reward model training based
# (set when reward model training used sft lora ckpt)
# SFT_LORA_CKPT="your_nebula_project.your_nebula_model/version=your_version"

# local_file或者oss挂载文件
INPUT="data/comparison_gpt4_data_zh.json"

# odps table
# ODPS_PROJECT="your_odps_project"
# INPUT="odps://your_odps_project/tables/your_odps_table/ds=xxx"

# output path of the evaluation result
OUTPUT="/data/oss_bucket_0/llm/reward_model_eval_result/"
EVAL_BATCH_SIZE=16

args="--stage rm \
    --model_name_or_path=$REWARD_CKPT \
    --do_eval True \
    --file_name=${INPUT} \
    --ranking \
    --prompt=instruction \
    --query=input \
    --chosen=chosen \
    --rejected=rejected \
    --template $PROMPT_TEMPLATE \
    --finetuning_type full \
    --output_dir ${OUTPUT} \
    --overwrite_cache \
    --per_device_eval_batch_size $EVAL_BATCH_SIZE \
    --logging_steps 1 \
    --cutoff_len=1024 \
    --preprocessing_num_workers=8 \
    --dataloader_num_workers=4 \
    --plot_loss \
    --report_to=ml_tracker \
    --bf16"

mdl_args="--queue=${QUEUE} \
        --entry="src/train_bash.py" \
        --worker_count=${WORLD_SIZE}  \
        --file.cluster_file=scripts/cluster.json \
        --oss_access_id=${OSS_ACCESS_ID} \
        --oss_access_key=${OSS_ACCESS_KEY} \
        --oss_bucket=${OSS_BUCKET} \
        --oss_endpoint=${OSS_ENDPOINT} \
        --algo_name=pytorch220"

if [ -n "${OPENLM_TOKEN}" ]; then
    mdl_args="${mdl_args} --env=OPENLM_TOKEN=${OPENLM_TOKEN}"
fi

if [ -n "${ODPS_PROJECT}" ]; then
    mdl_args="${mdl_args} --tables=${INPUT} --odps_project=${ODPS_PROJECT}"
fi

if [ -n "${TRACKER_PROJECT_NAME}" ]; then
    mdl_args="${mdl_args} --tracker_project_name=${TRACKER_PROJECT_NAME}"
fi

nebulactl run mdl --user_params="${args}"  $mdl_args
