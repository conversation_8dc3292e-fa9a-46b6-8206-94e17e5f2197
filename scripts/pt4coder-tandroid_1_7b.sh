#!/bin/bash
set +x

workdir=$(cd $(dirname $0); pwd)
source $workdir/config.sh

WORLD_SIZE=16
LR=2e-5
EPOCH=4
warmup_ratio=0.05
gradient_accumulation_steps=8
val_size=0.01

# 要加载的base模型
#MODEL_NAME="Qwen/Qwen2.5-Coder-7B-Instruct"
#MODEL_NAME="/data/xpfs_0/Qwen2.5_72B_Base/model/" # docstring的余弦退火阶段1
#MODEL_NAME="/data/xpfs_0/common/models/Qwen/Qwen3-8B-Base/" # docstring的余弦退火阶段1
#MODEL_NAME="/data/xpfs_0/common/models/Qwen/Qwen3-14B-Base/"
MODEL_NAME="/data/xpfs_0/common/models/Qwen/Qwen3-1.7B"

#DATASET="pt-c-fim-1,pt-c-fim-2,pt-java-fim-1,pt-java-fim-2,pt-java-fim-3,pt-java-fim-4,pt-java-fim-5,pt-swift-fim-1,pt-swift-fim-2,pt-swift-fim-3,pt-swift-fim-4,pt-swift-fim-5" # sft-block-fim-blank-etherll
#DATASET+=",pt-taotian-fim2,pt-taotian-fim1,pt-kotlin-fim"
DATASET="all-ev0709"
#DATASET="pt-merged-fim11-2"
#DATASET+=",pt-merged-fim-fixed" # 准备断点续训用的
DATASET+=",pt-wiki_zh_40m"

JOB_NAME="$MODEL_NAME-$VERSION"


args="--stage pt \
    --model_name_or_path=$MODEL_NAME \
    --do_train \
    --do_eval  \
    --val_size=$val_size \
    --eval_steps 50 \
    --dataset=$DATASET\
    --prompt=text \
    --template default \
    --finetuning_type full \
    --output_dir=/data/xpfs_0/1_7B_0719_3/ \
    --overwrite_cache \
    --per_device_eval_batch_size 1 \
    --per_device_train_batch_size 2 \
    --gradient_accumulation_steps $gradient_accumulation_steps \
    --lr_scheduler_type cosine \
    --logging_steps 1 \
    --save_steps 200 \
    --num_train_epochs $EPOCH \
    --learning_rate=$LR \
    --weight_decay=0.01 \
    --warmup_ratio $warmup_ratio \
    --cutoff_len=8192 \
    --packing False \
    --preprocessing_num_workers=64 \
    --dataloader_num_workers=4 \
    --plot_loss \
    --do_sample \
    --deepspeed=scripts/ds_zero3.json \
    --bf16"

mdl_args="--queue=${QUEUE} \
        --entry="src/train_bash.py" \
        --worker_count=${WORLD_SIZE}  \
        --file.cluster_file=scripts/cluster.json \
        --oss_access_id=${OSS_ACCESS_ID} \
        --oss_access_key=${OSS_ACCESS_KEY} \
        --oss_bucket=${OSS_BUCKET} \
        --job_name=${JOB_NAME} \
        --force \
        --job_success_notice=true \
        --oss_endpoint=${OSS_ENDPOINT} \
        --algo_name=pytorch240"

if [ -n "${OPENLM_TOKEN}" ]; then
    mdl_args="${mdl_args} --env=OPENLM_TOKEN=${OPENLM_TOKEN}"
fi

if [ -n "${ODPS_PROJECT}" ]; then
    mdl_args="${mdl_args} --tables=${ODPS_TABLE} --odps_project=${ODPS_PROJECT}"
fi

if [ -n "${TRACKER_PROJECT_NAME}" ]; then
    mdl_args="${mdl_args} --tracker_project_name=${TRACKER_PROJECT_NAME}"
fi

nebulactl run mdl --user_params="${args}"  $mdl_args
