#!/bin/bash
set +x

workdir=$(cd $(dirname $0); pwd)
source $workdir/config.sh

WORLD_SIZE=128
LR=5e-5
VERSION=$(date "+%Y-%m-%d-%H-%M-%S")
SAVE_MODEL="taobao_dev_mtl.chenniu-sft-fim/version=$VERSION"
#SAVE_MODEL="taobao_dev_mtl.chenniu-codegen25-codeEditor/version=v0"

PROMPT_TEMPLATE="empty"
#PROMPT_TEMPLATE="deepseek-v3"

#MODEL_NAME="deepseek-ai/DeepSeek-R1-Distill-Qwen-7B" # docstring的余弦退火阶段1
#MODEL_NAME="dfs://ea119dfssearch7--cn-shanghai/nebula/model/taobao_dev_mtl/chenniu-MTL-7B/2025-04-03-17-07-32_41830067d32e/checkpoint-4370"
#MODEL_NAME="/data/xpfs_0/data/model/code_completion_2.5_7B_0512/"
#MODEL_NAME="/data/xpfs_0/data/model/Qwen3-8B-Base/"
#MODEL_NAME="Qwen/Qwen2.5-Coder-7B"
MODEL_NAME="/data/xpfs_0/data/model/qwen_2.5_7B_0524_base_sft/"
#MODEL_NAME="/data/xpfs_0/data/model/qwen_2.5_7B_0603_base_sft/"
#MODEL_NAME="/data/xpfs_0/data/model/Qwen2.5-Coder-7B/Qwen2.5-Coder-7B/"
#MODEL_NAME="/data/xpfs_0/data/model/fim_pt/Qwen3-4B-base-0531/"
#MODEL_NAME="/data/xpfs_0/data/model/code_completion_2.5_7B_inner_0511/"
#MODEL_NAME="dfs://ea119dfssearch7--cn-shanghai/nebula/model/taobao_dev_mtl/chenniu-ds/2025-04-06-13-30-01_9d33f4017ca9/checkpoint-3900"
#MODEL_NAME="deepseek-ai/DeepSeek-R1-Distill-Qwen-7B"
# 数据集分区
#DATASET="sft-synthia-coder,sft-hf-open-data,sft-inner-code-ut,sft-outer-review,sft-inner-review,sft-commit-pack-multi,sft-code-repair,sft-code-gen,sft-codefeedback-cn,sft-commit-pack,sft-evol-data,sft-codeFeed"
#DATASET+=",sft-evol-data-cn,sft-evol-data-cn-1,sft-shoutao,sft-alpaca_gpt4_zh,sft-codealpaca,sft-evol-code-gen"
#DATASET+=",sft-math-qa" # 数学推理数据集
#DATASET="sft-inner-fim-0518,sft-outer-fim-0518,sft-cross-fim-0518,sft-outer2-fim-0518"
DATASET="sft-inner-fim-0603,sft-outer-fim-0605"

echo $DATASET
JOB_NAME="sft_code_fim-$VERSION"

args="--stage sft \
    --model_name_or_path=$MODEL_NAME \
    --do_train \
    --dataset=$DATASET\
    --prompt=instruction \
    --query=input \
    --response=output \
    --template=${PROMPT_TEMPLATE} \
    --finetuning_type full \
    --output_dir=/data/xpfs_0/data/model/qwen_2.5_7B_0605_base_sft/ \
    --overwrite_cache \
    --per_device_train_batch_size 4 \
    --per_device_eval_batch_size 1 \
    --gradient_accumulation_steps 4 \
    --lr_scheduler_type cosine \
    --logging_steps 1 \
    --save_steps 500 \
    --num_train_epochs 1 \
    --learning_rate=$LR \
    --packing False \
    --cutoff_len=8192 \
    --preprocessing_num_workers=64 \
    --warmup_ratio 0.15 \
    --dataloader_num_workers=4 \
    --plot_loss \
    --deepspeed=scripts/ds_zero3_cpuoffload.json \
    --bf16"

mdl_args="--queue=${QUEUE} \
        --entry="src/train_bash.py" \
        --worker_count=${WORLD_SIZE}  \
        --file.cluster_file=scripts/cluster.json \
        --oss_access_id=${OSS_ACCESS_ID} \
        --oss_access_key=${OSS_ACCESS_KEY} \
        --oss_bucket=${OSS_BUCKET} \
        --oss_endpoint=${OSS_ENDPOINT} \
        --force \
        --job_name=${JOB_NAME} \
        --job_success_notice=true \
        --algo_name=pytorch220"

if [ -n "${OPENLM_TOKEN}" ]; then
    mdl_args="${mdl_args} --env=OPENLM_TOKEN=${OPENLM_TOKEN}"
fi

if [ -n "${ODPS_PROJECT}" ]; then
    mdl_args="${mdl_args} --tables=${ODPS_TABLE} --odps_project=${ODPS_PROJECT}"
fi

if [ -n "${TRACKER_PROJECT_NAME}" ]; then
    mdl_args="${mdl_args} --tracker_project_name=${TRACKER_PROJECT_NAME}"
fi

export DS_SKIP_CUDA_CHECK=1
nebulactl run mdl --user_params="${args}"  $mdl_args