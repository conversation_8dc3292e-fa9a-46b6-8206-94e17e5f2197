#!/bin/bash
set +x

workdir=$(cd $(dirname $0); pwd)
source $workdir/config.sh

WORLD_SIZE=1

PROMPT_TEMPLATE=""
# mos uri / huggingface repo-id of inference model
MODEL_NAME=""

# lora ckpt mos_uri, split by `,` eg: mos_uri1,mos_uri2
# LORA_CKPT=""

# 生成模式，默认default，default是huggingface提供的默认方式，vllm是vllm库提供的并行生成模式
GENERATE_MODE="default"

args="--model_name_or_path=$MODEL_NAME \
      --infer_mode=$GENERATE_MODE \
      --template=$PROMPT_TEMPLATE"

if [ -n "${LORA_CKPT}" ]; then
    args="$args --adapter_name_or_path=$LORA_CKPT"
fi

JOB_NAME="api_demo_${MODEL_NAME}"

mdl_args="--queue=${QUEUE} \
          --entry=src/api_demo.py \
          --worker_count=${WORLD_SIZE}  \
          --file.cluster_file=scripts/cluster.json \
          --job_name=${JOB_NAME} \
          --algo_name=pytorch220"

nebulactl run mdl --user_params="${args}"  $mdl_args
