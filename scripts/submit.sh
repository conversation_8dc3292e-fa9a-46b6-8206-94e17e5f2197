#!/bin/bash

# =========== config =============
# 星云项目和队列
NEBULA_PROJECT=taobao_dev_mtl
QUEUE=taobao_dev_mtl

# 支持xpfs(cpfs/gpfs/...) 服务的集群，已默认在pod中挂载 /data/xpfs_0

# 关于oss挂载详细参考手册：https://aliyuque.antfin.com/uxctvg/gh8c24/xmbycq#Rw6B3
# 单oss bucket挂载到本地的路径是/data/oss_bucket_0/
# 如果要访问原bucket中oss path为：oss://your_bucket/dir1/dir2/file_name的文件，
# 对应的本地挂载路径是：/data/oss_bucket_0/dir1/dir2/file_name
# 可以使用 oss-browser 等工具检查OSS配置正确性
OSS_ACCESS_ID="LTAIGcY7idsIs0lm"
OSS_ACCESS_KEY="KM7z2PawYxDLqZWIFk7VraBUlZyBLk"
OSS_ENDPOINT="oss-cn-hangzhou-internal.aliyuncs.com"
OSS_BUCKET="mtl4-ai-resources-daily"

# src/dst 的参数逻辑与 linux cp命令一致
#DST_PATH="/data/oss_bucket_0/model_7b_32k/"
#SRC_PATH="/data/xpfs_0/ckpt_7B_32k/checkpoint-231/"
# ================================

#SRC_PATH="/data/xpfs_0/ckpt/checkpoint-100/"
#DST_PATH="/data/oss_bucket_0/ckpt/checkpoint-100/"
#SRC_PATH="/data/oss_bucket_0/data/PT/Token/"
#DST_PATH="/data/xpfs_0/data/PT/Token/"
#SRC_PATH="/data/oss_bucket_0/Qwen2.5-Coder-7B/"
#SRC_PATH="/data/oss_bucket_0/model/Seed-Coder-8B-Base/Seed-Coder-8B-Base/"
#SRC_PATH="/data/xpfs_0/data/model/code_completion_2.5_7B_inner_0511_no_pad/checkpoint-1500/"
#SRC_PATH="/data/xpfs_0/data/model/code_completion_2.5_7B_0512_sft/"
#SRC_PATH="/data/xpfs_0/ckpt_72B_32k_0513_quick_fix/"
#SRC_PATH="/data/xpfs_0/data/model/qwen_2.5_7B_0514_sft/"
#SRC_PATH="/data/xpfs_0/ckpt_72B_32k_0514_quick_fix/"
#SRC_PATH="/data/xpfs_0/data/model/qwen2.5-7B-base-fim/"
#SRC_PATH="/data/xpfs_0/data/model/qwen_2.5_7B_0520_base_sft/"
#SRC_PATH="/data/xpfs_0/data/model/qwen_2.5_7B_0521_base_sft/"
#SRC_PATH="/data/xpfs_0/data/model/AIMI-32B-0524-FIX/"
#SRC_PATH="/data/xpfs_0/data/model/AIMI-32B-0528/"
#SRC_PATH="/data/xpfs_0/data/model/qwen3_4B_0602_sft/"
# SRC_PATH="/data/xpfs_0/data/model/qwen_2.5_7B_0604_base_sft/"

#SRC_PATH="/data/xpfs_0/ckpt_4B_0708_4/"
SRC_PATH="/data/xpfs_0/14B_0723_3/"

#DST_PATH="/data/oss_bucket_0/model/ckpt_72B_32k_0513_quick_fix/"
#DST_PATH="/data/oss_bucket_0/model/qwen_2.5_7B_0514_sft/"
#DST_PATH="/data/oss_bucket_0/model/qwen2.5-7B-base-fim/"
#DST_PATH="/data/oss_bucket_0/model/base_fim/Qwen3-4B-base-0531/"

#DST_PATH="/data/oss_bucket_0/model/test/ckpt_4B_0708_4/"
DST_PATH="/data/oss_bucket_0/model/test/14B_0723_3/"

#DST_PATH="/data/xpfs_0/data/model/Qwen2.5-Coder-7B/"
#DST_PATH="/data/xpfs_0/data/model/Seed-Coder-8B-Base/"
#SRC_PATH="/data/oss_bucket_0/model/Qwen3-4B-Base/"
#DST_PATH="/data/xpfs_0/data/model/Qwen3-4B-Base/"
#SRC_PATH="/data/xpfs_0/ckpt_32B_0501/checkpoint-529/"
#DST_PATH="/data/oss_bucket_0/model/ckpt_32B_32k_0503/"
#SRC_PATH="/data/xpfs_0/ckpt_32B_0506/checkpoint-529/"
#DST_PATH="/data/oss_bucket_0/model/32B_32k_0508_epoch2/"
#SRC_PATH="/data/xpfs_0/data/model/code_completion_3_8B/checkpoint-9000/"
#SRC_PATH="/data/xpfs_0/data/model/code_completion_2.5_7B/checkpoint-1103/"
#SRC_PATH="/data/xpfs_0/data/model/code_completion_2.5_7B_inner/checkpoint-157/"
#SRC_PATH="/data/xpfs_0/data/model/code_completion_2.5_7B_inner_0510/"
#SRC_PATH="/data/xpfs_0/data/model/code_completion_2.5_7B_inner_0511_without_32k/"
#SRC_PATH="/data/xpfs_0/data/model/code_completion_2.5_7B_inner_0511/"
#DST_PATH="/data/oss_bucket_0/model/code_completion_2.5_7B_0511_full/"
#DST_PATH="/data/oss_bucket_0/model/code_completion_3_8B_0508/"
#DST_PATH="/data/oss_bucket_0/model/code_completion_2.5_7B_0509/"
#SRC_PATH="/data/xpfs_0/data/model/code_completion_2.5/checkpoint-6618/"
#DST_PATH="/data/oss_bucket_0/model/code_completion_2.5_0505/"
#SRC_PATH="/data/xpfs_0/ckpt_72B_16k/checkpoint-489/"
#SRC_PATH="/data/xpfs_0/ckpt_72B_16k_0423/checkpoint-335/"
#DST_PATH="/data/oss_bucket_0/model/ckpt_72B_16k_0423/"
#SRC_PATH="/data/xpfs_0/ckpt_72B_32k_0424/checkpoint-1/"
#DST_PATH="/data/oss_bucket_0/model/ckpt_72B_32k_0424_fix/"
#SRC_PATH="/data/xpfs_0/ckpt_72B_32k_0425/checkpoint-15"
#DST_PATH="/data/oss_bucket_0/model/ckpt_72B_32k_0425/"
#SRC_PATH="/data/xpfs_0/ckpt_72B_32k_0425_beta1/checkpoint-42/"
#SRC_PATH="/data/xpfs_0/ckpt_72B_32k_0427/checkpoint-59/"
#SRC_PATH="/data/xpfs_0/ckpt_72B_32k_0427_quick_fix_3/checkpoint-63"
#SRC_PATH="/data/xpfs_0/ckpt_72B_32k_0427_quick_fix_4/checkpoint-63/"
#SRC_PATH="/data/xpfs_0/ckpt_72B_32k_0428_quick_fix_1/checkpoint-87/"
#DST_PATH="/data/oss_bucket_0/model/ckpt_72B_32k_0428_qfix/"
#DST_PATH="/data/xpfs_0/data/model/Qwen3-32B/"
#SRC_PATH="/data/oss_bucket_0/model/Qwen3-32B/"

args="--src_path=${SRC_PATH} --dst_path=${DST_PATH}"

nebulactl run mdl\
    --nebula_project=$NEBULA_PROJECT \
    --queue=$QUEUE \
    --job_name=oss_xpfs_copy_job\
    --entry=ckpt_copyfile.py \
    --worker_count=1  \
    --user_params="$args" \
    --file.cluster_file=./cluster.json \
    --algo_name=pytorch240 \
    --oss_endpoint=$OSS_ENDPOINT \
    --oss_bucket=$OSS_BUCKET \
    --oss_access_id=$OSS_ACCESS_ID \
    --oss_access_key=$OSS_ACCESS_KEY