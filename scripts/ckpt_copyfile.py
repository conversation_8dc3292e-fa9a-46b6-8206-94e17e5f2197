import os
import argparse
import shutil

if __name__ == "__main__":
    # parse args
    parser = argparse.ArgumentParser()
    parser.add_argument("--src_path", type=str, required=True)
    parser.add_argument("--dst_path", type=str, required=True)
    args = parser.parse_args()

    src_path = args.src_path
    dst_path = args.dst_path

    # check src_path
    if not os.path.exists(src_path):
        raise ValueError(f"src_path {src_path} does not exist")

    # check and prepare dst_path
    # parse first two level of dst_path
    dst_path_parts = dst_path.split("/")
    if len(dst_path_parts) < 2:
        raise ValueError(f"dst_path {dst_path} is not valid")
    dst_base = f"/{dst_path_parts[0]}/{dst_path_parts[1]}"
    # check dst_base exists
    if not os.path.exists(dst_base):
        raise ValueError(f"dst_base {dst_base} does not exist")

    # prepare dst_path
    # if src_path is file && dst_path is not ended with / && src_path's filename is same as dst_path's filename
    # then dst_path is file, then just create dst_path's parent dir
    if os.path.isfile(src_path) and not dst_path.endswith("/") and src_path.split()[-1] == dst_path.split()[-1]:
        dst_path = "/".join(dst_path.split("/")[:-1])
    os.makedirs(dst_path, exist_ok=True)

    # 处理单个文件的情况
    if os.path.isfile(src_path):
        shutil.copy2(src_path, dst_path)
    else:
        # 只复制src_path目录下的文件，不复制子目录
        for item in os.listdir(src_path):
            item_path = os.path.join(src_path, item)
            if os.path.isfile(item_path):
                shutil.copy2(item_path, dst_path)
