# 关于oss挂载详细参考手册：https://aliyuque.antfin.com/uxctvg/gh8c24/xmbycq#Rw6B3
## oss bucket挂载到本地的路径是/data/oss_bucket_0/
## 如果要访问原bucket中oss path为：oss://your_bucket/dir1/dir2/file_name的文件，
## 对应的本地挂载路径是：/data/oss_bucket_0/dir1/dir2/file_name

OSS_ACCESS_ID="LTAIGcY7idsIs0lm"
OSS_ACCESS_KEY="KM7z2PawYxDLqZWIFk7VraBUlZyBLk"
OSS_ENDPOINT="oss-cn-hangzhou-internal.aliyuncs.com"
#OSS_ENDPOINT="oss-cn-hangzhou.aliyuncs.com"
OSS_BUCKET="mtl4-ai-resources-daily"
# set this when you use odps
# ODPS_PROJECT="your_odps_project"
# ODPS_TABLE="odps://your_odps_project/tables/your_odps_table/ds=xxx"

# 如果你用到 OpenLM Datasets 请先申请 Token
# https://aliyuque.antfin.com/alimama-nebula/vbw69h/tkwsyrgee6rof6po
# OPENLM_TOKEN="OPENLM_451811_bTFnsZtgdOhzaHcgQzfBtfsPuGAZTkUG"

# 星云训练队列名
#QUEUE="erup"
#QUEUE="mtl_llm_dev_h20"
QUEUE="taobao_dev_mtl"
# QUEUE="erup"
