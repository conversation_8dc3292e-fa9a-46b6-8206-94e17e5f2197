#!/bin/bash
set +x

workdir=$(cd $(dirname $0); pwd)
source $workdir/config.sh

WORLD_SIZE=32
LR=4e-5
VERSION=$(date "+%Y-%m-%d-%H-%M-%S")
SAVE_MODEL="taobao_dev_mtl.chenniu-ds/version=$VERSION"
#SAVE_MODEL="taobao_dev_mtl.chenniu-codegen25-codeEditor/version=v0"

PROMPT_TEMPLATE="qwen2_5"

#MODEL_NAME="dfs://ea119dfssearch7--cn-shanghai/nebula/model/taobao_dev_mtl/chenniu-MTL-7B/2025-04-08-21-15-47_bf2fc13f8fa4/checkpoint-1365"
MODEL_NAME="/data/xpfs_0/PT_MODEL_04_08/PT_MODEL_04_08/checkpoint-1365/"

# 数据集分区
#DATASET="sft-synthia-coder,sft-hf-open-data,sft-inner-code-ut,sft-outer-review,sft-inner-review,sft-commit-pack-multi,sft-code-repair,sft-code-gen,sft-codefeedback-cn,sft-commit-pack,sft-evol-data,sft-codeFeed"
#DATASET+=",sft-evol-data-cn,sft-evol-data-cn-1,sft-shoutao,sft-alpaca_gpt4_zh,sft-codealpaca,sft-evol-code-gen"
#DATASET+=",sft-math-qa" # 数学推理数据集
DATASET="sft-mtl-instruction,sft-identity,sft-shoutao-QA" # 身份认证数据集 # 1k
#DATASET+=",multi-function-call" # 多轮对话数据集
#DATASET+=",sft-opencoder-stage2" # OpenCoder-stage2SFT的数据集
#DATASET+=",sft-docstring_badcase,sft-docstring-v2,sft-docstring-upgrade,sft-swift-docstring,sft-java-docstring" # Docstring相关数据集
#DATASET="sft-codeRule-think-8k"
#DATASET="sft-codeRule-0313" # 代码规则匹配
DATASET+=",cot-kodcode-32k,sft-easy-dataset" # 1w
DATASET+=",cot-r1-110k" # 11w

#DATASET+=",cot-codeforce-8k,cot-codeforce-16k"
DATASET+=",cot-openr1-coder" # 4w
DATASET+=",cot-fei-1k" # 1k
#DATASET+=",sft-coderuler-java,sft-coderuler-neg,sft-coderuler-android,sft-coderuler-bc,sft-bai-neg,sft-coderuler-1126" # ruler规则数据集
#DATASET+=",sft-coderuler-ios,sft-bai-train,sft-coderuler-bc2" # ruler规则数据集
echo $DATASET
JOB_NAME="EditorV2bs8cut8K-$VERSION"

args="--stage sft \
    --model_name_or_path=$MODEL_NAME \
    --do_train \
    --do_eval  \
    --val_size=0.005 \
    --eval_steps 50 \
    --dataset=$DATASET\
    --tokenized_path /data/xpfs_0/dataset/mtl_coder_16k \
    --prompt=instruction \
    --query=input \
    --response=output \
    --template=${PROMPT_TEMPLATE} \
    --finetuning_type full \
    --output_dir=/data/xpfs_0/ckpt/ \
    --overwrite_cache \
    --per_device_train_batch_size 2 \
    --evaluation_strategy steps \
    --per_device_eval_batch_size 1 \
    --gradient_accumulation_steps 8 \
    --lr_scheduler_type cosine \
    --logging_steps 1 \
    --save_steps 100 \
    --num_train_epochs 3 \
    --learning_rate=$LR \
    --lora_rank 8 \
    --packing False \
    --cutoff_len=16384 \
    --preprocessing_num_workers=8 \
    --warmup_ratio 0.15 \
    --dataloader_num_workers=4 \
    --plot_loss \
    --deepspeed=scripts/ds_zero3_cpuoffload.json \
    --bf16"

mdl_args="--queue=${QUEUE} \
        --entry="src/train_bash.py" \
        --worker_count=${WORLD_SIZE}  \
        --file.cluster_file=scripts/cluster.json \
        --oss_access_id=${OSS_ACCESS_ID} \
        --oss_access_key=${OSS_ACCESS_KEY} \
        --oss_bucket=${OSS_BUCKET} \
        --oss_endpoint=${OSS_ENDPOINT} \
        --force \
        --job_name=${JOB_NAME} \
        --job_success_notice=true \
        --algo_name=pytorch220"


if [ -n "${ODPS_PROJECT}" ]; then
    mdl_args="${mdl_args} --tables=${ODPS_TABLE} --odps_project=${ODPS_PROJECT}"
fi

if [ -n "${TRACKER_PROJECT_NAME}" ]; then
    mdl_args="${mdl_args} --tracker_project_name=${TRACKER_PROJECT_NAME}"
fi

export DS_SKIP_CUDA_CHECK=1
nebulactl run mdl --user_params="${args}"  $mdl_args