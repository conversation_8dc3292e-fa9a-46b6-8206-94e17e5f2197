[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.ruff]
target-version = "py38"
line-length = 119
indent-width = 4

[tool.ruff.lint]
ignore = ["C408", "C901", "E501", "E731", "E741", "W605"]
select = ["C", "E", "F", "I", "W"]

[tool.ruff.lint.isort]
lines-after-imports = 2
known-first-party = ["llmtuner"]
known-third-party = [
    "accelerate",
    "datasets",
    "gradio",
    "numpy",
    "peft",
    "torch",
    "transformers",
    "trl"
]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"
