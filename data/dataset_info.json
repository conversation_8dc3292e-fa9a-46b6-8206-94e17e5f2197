{"sft-alpaca_gpt4_zh": {"file_name": "/data/oss_bucket_0/data/SFT/sft-alpaca_gpt4_data_zh.json", "file_sha1": "3eaa3bda364ccdd59925d7448a698256c31ef845"}, "sft-identity": {"file_name": "/data/oss_bucket_0/data/SFT/sft-mtl_instruction.json", "file_sha1": "身份识别的信息，比如问你是谁这种问题"}, "sft-q3-identity": {"file_name": "/data/oss_bucket_0/data/COT/qwen3/add_think/sft-mtl_instruction.jsonl", "file_sha1": "身份识别的信息，比如问你是谁这种问题"}, "sft_extra_data": {"file_name": "/data/oss_bucket_0/data/SFT/extra_dataset.jsonl", "file_sha1": "一些额外的数据"}, "sft-evol-data-cn": {"file_name": "/data/oss_bucket_0/data/SFT/sft-evol-gpt4-data-cn.json", "file_sha1": "ffe3ecb58ab642da33fbb514d5e6188f1469ad40"}, "sft-evol-data": {"file_name": "/data/oss_bucket_0/data/SFT/EvolInstruct-Code-80k.jsonl", "file_sha1": "包含一些编程语言的英文数据集，配比质量比较高"}, "sft-commit-pack": {"file_name": "/data/oss_bucket_0/data/SFT/commitpack_train.jsonl", "file_sha1": "个人开源的commit数据集，已经完成清洗"}, "sft-commit-pack-multi": {"file_name": "/data/oss_bucket_0/data/SFT/commitpack_multi_train.jsonl", "file_sha1": "个人开源的commit数据集多语言，已经完成清洗"}, "sft-code-repair": {"file_name": "/data/oss_bucket_0/data/SFT/codebench_repair_train.jsonl", "file_sha1": "个人开源的补全（repair）数据集python，已经完成清洗"}, "sft-evol-data-cn-1": {"file_name": "/data/oss_bucket_0/data/SFT/sft-data-evol_cn-41256.json", "file_sha1": "从英文翻译的代码类数据集"}, "sft-java-docstring": {"file_name": "/data/oss_bucket_0/data/SFT/sft-java-docstring.json", "file_sha1": "ffe3ecb58ab642da33fbb514d5e6188f1469ad40"}, "sft-python-code-exercise": {"file_name": "/data/oss_bucket_0/data/SFT/sft-python-code-exercise.json", "file_sha1": "ffe3ecb58ab642da33fbb514d5e6188f1469ad40"}, "sft-mtl-instruction": {"file_name": "/data/oss_bucket_0/data/SFT/sft-mtl_instruction.json", "file_sha1": "ffe3ecb58ab642da33fbb514d5e6188f1469ad40"}, "sft-swift-code-gen": {"file_name": "/data/oss_bucket_0/data/SFT/sft-swiftCodeGen.json", "file_sha1": "ffe3ecb58ab642da33fbb514d5e6188f1469ad40"}, "sft-evol-code-gen": {"file_name": "/data/oss_bucket_0/data/SFT/sft-evol-Instruction-66k.json", "file_sha1": "开源项目，合成数据后经过进一步清洗得到的"}, "sft-docstring-upgrade": {"file_name": "/data/oss_bucket_0/data/SFT/end_code_docstring_gpt4_instruction.jsonl", "file_sha1": "ffe3ecb58ab642da33fbb514d5e6188f1469ad40"}, "sft-docstring-v2": {"file_name": "/data/oss_bucket_0/data/SFT/docstring_v2_training_sft.jsonl", "file_sha1": "ffe3ecb58ab642da33fbb514d5e6188f1469ad40"}, "sft-ali-codeRuler": {"file_name": "/data/oss_bucket_0/data/SFT/ruler_gpt4.jsonl", "file_sha1": "ffe3ecb58ab642da33fbb514d5e6188f1469ad40"}, "sft-real_codeRuler": {"file_name": "/data/oss_bucket_0/data/SFT/real_code_ruler.jsonl", "file_sha1": "ffe3ecb58ab642da33fbb514d5e6188f1469ad40"}, "sft-code-gen": {"file_name": "/data/oss_bucket_0/data/SFT/updated_train-10k.jsonl", "file_sha1": "开源配比的代码数据集"}, "sft-code-gen-real": {"file_name": "/data/oss_bucket_0/data/SFT/inner_docstring2code.jsonl", "file_sha1": "终端代码生成数据，来源docstring大模型"}, "sft-codefeedback-cn": {"file_name": "/data/oss_bucket_0/data/SFT/CodeFeedback-Filtered-Instruction-Chinese.jsonl", "file_sha1": "中文的文本代码补全工作"}, "sft-codebench-gen": {"file_name": "/data/oss_bucket_0/data/SFT/codebench_textgen_train.jsonl", "file_sha1": "文本代码补全"}, "sft-math-qa": {"file_name": "/data/oss_bucket_0/data/SFT/mathqa.jsonl", "file_sha1": "数学相关的数据，用于数据配比工作，提升回复质量"}, "sft-tiny-code": {"file_name": "/data/oss_bucket_0/data/SFT/tiny-codes.jsonl", "file_sha1": "文本代码生成，数据内容比较多"}, "sft-inner-review": {"file_name": "/data/oss_bucket_0/data/SFT/inner_gpt_code_review.jsonl", "file_sha1": "将内部代码进行清洗混淆后交给GPT4进行生成"}, "sft-outer-review": {"file_name": "/data/oss_bucket_0/data/SFT/outter_gpt_code_review.jsonl", "file_sha1": "将外部代码进行清洗混淆后交给GPT4进行生成"}, "pt-code-fim-0528": {"file_name": "/data/oss_bucket_0/data/PT/0528-fim/", "file_sha1": "ffe3ecb58ab642da33fbb514d5e6188f1469ad40", "columns": {"prompt": "text"}}, "sft-inner-fim": {"file_name": "/data/oss_bucket_0/data/SFT/0512/sft_fim.jsonl", "file_sha1": "random数据构造成SFT数据"}, "multi-function-call": {"file_name": "/data/oss_bucket_0/data/SFT/multi_function_call.json", "file_sha1": "多轮代码生成对话数据", "formatting": "sharegpt", "columns": {"messages": "messages"}, "tags": {"role_tag": "role", "content_tag": "content", "user_tag": "user", "assistant_tag": "assistant", "system_tag": "system"}}, "q3-multi-function-call": {"file_name": "/data/oss_bucket_0/data/COT/qwen3/add_think/multi_function_call.json", "file_sha1": "多轮代码生成对话数据", "formatting": "sharegpt", "columns": {"messages": "messages"}, "tags": {"role_tag": "role", "content_tag": "content", "user_tag": "user", "assistant_tag": "assistant", "system_tag": "system"}}, "q3-multi-computer": {"file_name": "/data/oss_bucket_0/data/COT/qwen3/add_think/computer_zh_processed.json", "file_sha1": "多轮代码生成对话数据", "formatting": "sharegpt", "columns": {"messages": "messages"}, "tags": {"role_tag": "role", "content_tag": "content", "user_tag": "user", "assistant_tag": "assistant", "system_tag": "system"}}, "q3-multi-turn": {"file_name": "/data/oss_bucket_0/data/COT/qwen3/add_think/processed_data.json", "file_sha1": "多轮代码生成对话数据", "formatting": "sharegpt", "columns": {"messages": "messages"}, "tags": {"role_tag": "role", "content_tag": "content", "user_tag": "user", "assistant_tag": "assistant", "system_tag": "system"}}, "multi-code-feedback": {"file_name": "/data/oss_bucket_0/data/SFT/Code-Feedback.jsonl", "file_sha1": "多轮代码生成对话数据", "formatting": "sharegpt", "columns": {"messages": "messages"}, "tags": {"role_tag": "role", "content_tag": "content", "user_tag": "user", "assistant_tag": "assistant"}}, "instruction-under-enhance": {"file_name": "/data/oss_bucket_0/data/SFT/Code-Feedback.jsonl", "file_sha1": "开源方通过AUTOIF获取得到的关于指令增强的数据集", "formatting": "sharegpt", "columns": {"messages": "messages"}, "tags": {"role_tag": "role", "content_tag": "content", "user_tag": "user", "assistant_tag": "assistant"}}, "sft-json-code-gen-real": {"file_name": "/data/oss_bucket_0/data/SFT/code_gen_0812.jsonl", "file_sha1": "ffe3ecb58ab642da33fbb514d5e6188f1469ad40"}, "sft-hf-open-data": {"file_name": "/data/oss_bucket_0/data/SFT/hg_data_code.json", "file_sha1": "开源上的数据集，包含多种编程语言，主要涉及代码类型数据"}, "sft-ruler-complex": {"file_name": "/data/oss_bucket_0/data/SFT/complex_ruler.jsonl", "file_sha1": "ffe3ecb58ab642da33fbb514d5e6188f1469ad40"}, "eval_answer": {"file_name": "/data/oss_bucket_0/data/SFT/eval.jsonl", "file_sha1": "补充用数据集"}, "sft-codealpaca": {"file_name": "/data/oss_bucket_0/data/SFT/sft-code_alpaca_20k.json", "file_sha1": "AI-ModelScope/CodeAlpaca-20k"}, "sft-codeFeed": {"file_name": "/data/oss_bucket_0/data/SFT/codeFeed_cn.jsonl", "file_sha1": "AI-ModelScope/CodeAlpaca-20k"}, "sft-swift-docstring": {"file_name": "/data/oss_bucket_0/data/SFT/sft-swift-docstring-gpt4.json", "file_sha1": "AI-ModelScope/CodeAlpaca-20k"}, "sft-inner-code-ut": {"file_name": "/data/oss_bucket_0/data/SFT/inner_code_UT.jsonl", "file_sha1": "通过内部代码生成的单元测试用例的数据"}, "sft-qa-generate": {"file_name": "/data/oss_bucket_0/data/SFT/self-QA-generate.jsonl", "file_sha1": "AI-ModelScope/CodeAlpaca-20k"}, "sft-coderuler-android": {"file_name": "/data/oss_bucket_0/data/SFT/android_ruler.json", "file_sha1": "规约生成的数据"}, "sft-synthia-coder": {"file_name": "/data/oss_bucket_0/data/SFT/Synthia-Coder-v1.5.jsonl", "file_sha1": "开源的代码合成数据集"}, "sft-coderuler-neg": {"file_name": "/data/oss_bucket_0/data/SFT/negative_samples_in_train.json", "file_sha1": "规约生成的数据训练集中的负样本"}, "sft-block-fim": {"file_name": "/data/oss_bucket_0/data/SFT/block_fim_sft.jsonl", "file_sha1": "block数据构造成SFT数据"}, "android-test": {"file_name": "/data/oss_bucket_0/test/pt_android_dataset_0606.jsonl", "file_sha1": "Android PT测试数据", "columns": {"prompt": "text"}}, "android-test1": {"file_name": "/data/oss_bucket_0/test/pt_android_dataset_0608_v2.jsonl", "file_sha1": "Android-PT测试数据1", "columns": {"prompt": "text"}}, "sft-block-fim2": {"file_name": "/data/oss_bucket_0/data/SFT/block_fim_sft_twice.jsonl", "file_sha1": "block数据构造成SFT数据"}, "sft-func-block-fim": {"file_name": "/data/oss_bucket_0/data/SFT/func_block_fim_sft.jsonl", "file_sha1": "函数block数据构造成SFT数据"}, "sft-block-fim-blank": {"file_name": "/data/oss_bucket_0/data/SFT/block_fim_sft-blank.jsonl", "file_sha1": "block数据构造成SFT数据"}, "sft-block-fim-blank-starcoder": {"file_name": "/data/oss_bucket_0/data/SFT/starcoder_fim_data.jsonl", "file_sha1": "block数据构造成SFT数据"}, "sft-block-fim-blank-sourcegraph": {"file_name": "/data/oss_bucket_0/data/SFT/sourcegraph_fim_data.jsonl", "file_sha1": "block数据构造成SFT数据"}, "sft-block-fim-blank-etherll": {"file_name": "/data/oss_bucket_0/data/SFT/etherll_fim_data.jsonl", "file_sha1": "block数据构造成SFT数据"}, "sft-random-fim": {"file_name": "/data/oss_bucket_0/data/SFT/random_fim_sft.jsonl", "file_sha1": "random数据构造成SFT数据"}, "sft-random-fim-key": {"file_name": "/data/oss_bucket_0/data/SFT/random_fim_sft_keyword.jsonl", "file_sha1": "random数据构造成SFT数据"}, "sft-random-fim-outer": {"file_name": "/data/oss_bucket_0/data/SFT/random_fim_sft_outer.jsonl", "file_sha1": "random数据构造成SFT数据"}, "sft-random-fim-8k": {"file_name": "/data/oss_bucket_0/data/SFT/fim_8k.jsonl", "file_sha1": "random数据构造成SFT数据"}, "sft-codeRule-8k": {"file_name": "/data/oss_bucket_0/data/SFT/output_8k.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "sft-codeRule-hw": {"file_name": "/data/oss_bucket_0/data/SFT/cleaned_data(1).jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "sft-codeRule-without-thinking": {"file_name": "/data/oss_bucket_0/data/SFT/rule_match_without_think.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "cot-coder-40k": {"file_name": "/data/oss_bucket_0/data/COT/coder_40k.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "cot-16k": {"file_name": "/data/oss_bucket_0/data/COT/token_bucket/token_bucket/0-16K.jsonl", "file_sha1": "cot数据处理后得到的0-16k数据"}, "cot-q3-16k": {"file_name": "/data/oss_bucket_0/data/COT/qwen3/add_think/8K-20K.jsonl", "file_sha1": "cot数据处理后得到的0-16k数据"}, "cot-8k": {"file_name": "/data/oss_bucket_0/data/COT/token_bucket/token_bucket/0-8K.jsonl", "file_sha1": "cot数据处理后得到的0-16k数据"}, "cot-q3-8k": {"file_name": "/data/oss_bucket_0/data/COT/qwen3/add_think/0-8K.jsonl", "file_sha1": "cot数据处理后得到的0-16k数据"}, "cot-q3-fix": {"file_name": "/data/oss_bucket_0/data/COT/qwen3/add_think/q3_fix.jsonl", "file_sha1": "cot数据处理后得到的0-16k数据"}, "cot-q3-self": {"file_name": "/data/oss_bucket_0/data/COT/qwen3/add_think/sft-mtl_instruction.jsonl", "file_sha1": "cot数据处理后得到的0-16k数据"}, "cot-ut": {"file_name": "/data/oss_bucket_0/data/COT/ut_cot.jsonl", "file_sha1": "cot的UT数据，最大字符8600token"}, "cot-q3-ut": {"file_name": "/data/oss_bucket_0/data/COT/qwen3/add_think/ut_cot.jsonl", "file_sha1": "cot的UT数据，最大字符8600token"}, "cot-extra": {"file_name": "/data/oss_bucket_0/data/COT/0424_extra.jsonl", "file_sha1": "cot的UT数据，最大字符8600token"}, "cot-q3-extra": {"file_name": "/data/oss_bucket_0/data/COT/qwen3/add_think/0424_extra.jsonl", "file_sha1": "cot的UT数据，最大字符8600token"}, "cot-quickfix": {"file_name": "/data/oss_bucket_0/data/COT/sdk_quick_fix.jsonl", "file_sha1": "cot的UT数据，最大字符8600token"}, "cot-q3-quickfix": {"file_name": "/data/oss_bucket_0/data/COT/qwen3/add_think/sdk_quick_fix.jsonl", "file_sha1": "cot的UT数据，最大字符8600token"}, "cot-20k": {"file_name": "/data/oss_bucket_0/data/COT/token_bucket/token_bucket/8K-20K.jsonl", "file_sha1": "cot数据处理后得到的0-16k数据"}, "cot-q3-20k": {"file_name": "/data/oss_bucket_0/data/COT/qwen3/add_think/8K-20K.jsonl", "file_sha1": "cot数据处理后得到的0-16k数据"}, "cot-q3-32k": {"file_name": "/data/oss_bucket_0/data/COT/qwen3/add_think/20K+.jsonl", "file_sha1": "cot数据处理后得到的16-32k数据"}, "cot-kodcode-32k": {"file_name": "/data/oss_bucket_0/data/COT/kodcode_32k.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "sft-easy-dataset": {"file_name": "/data/oss_bucket_0/data/SFT/easy_dataset.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "cot-openr1-coder": {"file_name": "/data/oss_bucket_0/data/COT/openr1_coder.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "cot-r1-110k": {"file_name": "/data/oss_bucket_0/data/COT/r1_sft.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "cot-fei-1k": {"file_name": "/data/oss_bucket_0/data/COT/s1k.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "cot-codeforce-8k": {"file_name": "/data/oss_bucket_0/data/COT/filtered_data_8k.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "cot-codeforce-16k": {"file_name": "/data/oss_bucket_0/data/COT/filtered_data_16k.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "sft-codeRule-think-8k": {"file_name": "/data/oss_bucket_0/data/SFT/think_8k_code_rule.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "sft-codeRule-without-think-8k": {"file_name": "/data/oss_bucket_0/data/SFT/without_think_8k_code_rule.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "sft-codeRule-16k": {"file_name": "/data/oss_bucket_0/data/SFT/output_16k.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "sft-bai-train": {"file_name": "/data/oss_bucket_0/data/SFT/bai_train.json", "file_sha1": "白老师的数据生成训练数据，2.7K左右"}, "sft-bai-neg": {"file_name": "/data/oss_bucket_0/data/SFT/bai_negative.json", "file_sha1": "白老师的数据生成训练数据的负样本"}, "sft-coderuler-bc": {"file_name": "/data/oss_bucket_0/data/SFT/badcaserepair.json", "file_sha1": "应用过程中生成的badCase需要修复"}, "sft-coderuler-bc2": {"file_name": "/data/oss_bucket_0/data/SFT/badcase.json", "file_sha1": "应用过程中生成的badCase需要修复"}, "sft-coderuler-java": {"file_name": "/data/oss_bucket_0/data/SFT/ali_java.json", "file_sha1": "阿里巴巴强制规约204条数据生成的样本数据集"}, "sft-coderuler-1126": {"file_name": "/data/oss_bucket_0/data/SFT/mixed5k.jsonl", "file_sha1": "阿里巴巴强制规约204条数据生成的样本数据集"}, "sft-coderuler-ios": {"file_name": "/data/oss_bucket_0/data/SFT/IOS_ruler.json", "file_sha1": "规约生成的数据"}, "sft-shoutao": {"file_name": "/data/oss_bucket_0/data/SFT/sft-shoutao-processed.json", "file_sha1": "手工收集的组件知识数据"}, "sft-opencoder-stage2": {"file_name": "/data/oss_bucket_0/data/SFT/opencoder_stage_2.jsonl", "file_sha1": "opencoder开源的SFT第二阶段数据集"}, "sft-shoutao-QA": {"file_name": "/data/oss_bucket_0/data/SFT/idea_gen_train.jsonl", "file_sha1": "RAG知识生成训练集-ideaTalk"}, "sft-q3-shoutao-QA": {"file_name": "/data/oss_bucket_0/data/COT/qwen3/add_think/idea_gen_train.jsonl", "file_sha1": "RAG知识生成训练集-ideaTalk"}, "sft-docstring_badcase": {"file_name": "/data/oss_bucket_0/data/SFT/badCaseCorrect.jsonl", "file_sha1": "Docstring_a10_v0中的一些bad case修复，通过内部大模型的重新润色进行修正，解决Orange组件和特有Toast翻译异常的问题"}, "computer_knowledge": {"file_name": "/data/oss_bucket_0/data/SFT/computer_zh.jsonl", "file_sha1": "开源，shareAI/ShareGPT-Chinese-English-90k中抽取的和代码相关的内容", "formatting": "sharegpt", "columns": {"messages": "messages"}}, "Code290k": {"file_name": "/data/oss_bucket_0/data/SFT/Code-290k-ShareGPT.json", "formatting": "sharegpt", "columns": {"messages": "conversations"}}, "pt-coderule": {"file_name": "/data/oss_bucket_0/data/PT/CodeRule.txt", "file_sha1": "e70375e28eda542a90c68213640cc371898ce181", "columns": {"prompt": "text"}}, "pt-wiki_zh": {"file_name": "/data/oss_bucket_0/data/PT/wikipedia-cn-20230720-filtered.json", "file_sha1": "PT格式的中文百科数据，其中大部分都是一些地理或者历史信息，补充中文数据理解能力", "columns": {"prompt": "completion"}}, "pt-c-fim-1": {"file_name": "/data/oss_bucket_0/data/PT/c++_fim_1.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-c-fim-2": {"file_name": "/data/oss_bucket_0/data/PT/c++_fim_2.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-java-fim": {"file_name": "/data/oss_bucket_0/data/PT/java.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-java-fim-1": {"file_name": "/data/oss_bucket_0/data/PT/java_fim_1.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-java-fim-2": {"file_name": "/data/oss_bucket_0/data/PT/java_fim_2.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-java-fim-3": {"file_name": "/data/oss_bucket_0/data/PT/java_fim_3.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-java-fim-4": {"file_name": "/data/oss_bucket_0/data/PT/java_fim_4.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-java-fim-5": {"file_name": "/data/oss_bucket_0/data/PT/java_fim_5.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-java-fim-6": {"file_name": "/data/oss_bucket_0/data/PT/java_fim_6.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-swift-fim-1": {"file_name": "/data/oss_bucket_0/data/PT/swift_fim_1.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-swift-fim-2": {"file_name": "/data/oss_bucket_0/data/PT/swift_fim_2.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-swift-fim-3": {"file_name": "/data/oss_bucket_0/data/PT/swift_fim_3.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-swift-fim-4": {"file_name": "/data/oss_bucket_0/data/PT/swift_fim_4.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-swift-fim-5": {"file_name": "/data/oss_bucket_0/data/PT/swift_fim_5.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-objc-fim-1": {"file_name": "/data/oss_bucket_0/data/PT/0510/objcset_part1.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-objc-fim-2": {"file_name": "/data/oss_bucket_0/data/PT/0510/objcset_part2.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-objc-fim-3": {"file_name": "/data/oss_bucket_0/data/PT/0510/objcset_part3.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-objc-fim-4": {"file_name": "/data/oss_bucket_0/data/PT/0510/objcset_part4.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-kotlin-fim": {"file_name": "/data/oss_bucket_0/data/PT/kotlin_fim_1.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-kotlin-fim-1": {"file_name": "/data/oss_bucket_0/data/PT/0510/kotlin_part1.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-kotlin-fim-2": {"file_name": "/data/oss_bucket_0/data/PT/0510/kotlin_part2.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-kotlin-fim-3": {"file_name": "/data/oss_bucket_0/data/PT/0510/kotlin_part3.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-merged-fim3": {"file_name": "/data/oss_bucket_0/data/PT/pt_fim_3.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-merged-fim2": {"file_name": "/data/oss_bucket_0/data/PT/pt_fim_2.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-merged-fim1": {"file_name": "/data/oss_bucket_0/data/PT/pt_fim_1.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-taotian-fim3": {"file_name_v1": "/data/oss_bucket_0/data/PT/0510/taotian_fim_1.jsonl", "file_name": "/data/oss_bucket_0/data/PT/0510/cutoff_length_dataset_part3.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-taotian": {"file_name_v1": "/data/oss_bucket_0/data/PT/0510/processed_fim_dataset.jsonl", "file_name": "/data/oss_bucket_0/data/PT/0510/processed_fim_dataset.jsonl", "file_sha1": "这个是不手动补全的", "columns": {"prompt": "text"}}, "pt-taotian_nopad": {"file_name": "/data/oss_bucket_0/data/PT/0510/taobao_nopad1.jsonl", "file_name_old": "/data/oss_bucket_0/data/PT/0510/processed_fim_dataset.jsonl", "file_sha1": "这个是不手动补全的", "columns": {"prompt": "text"}}, "pt-taotian-32k": {"file_name_v1": "/data/oss_bucket_0/data/PT/0510/fim_32k.jsonl", "file_name": "/data/oss_bucket_0/data/PT/0510/cutoff_length_dataset_part3.jsonl", "file_sha1": "这个是不手动补全的", "columns": {"prompt": "text"}}, "pt-taotian-8k": {"file_name": "/data/oss_bucket_0/data/PT/0510/fim_8k.jsonl", "file_name_1": "/data/oss_bucket_0/data/PT/0510/cutoff_length_dataset_part3.jsonl", "file_sha1": "这个是不手动补全的", "columns": {"prompt": "text"}}, "pt-taotian-fim1": {"file_name_v1": "/data/oss_bucket_0/data/PT/taotian_fim_1.jsonl", "file_name": "/data/oss_bucket_0/data/PT/0510/cutoff_length_dataset_part1.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-taotian-fim2": {"file_name_v1": "/data/oss_bucket_0/data/PT/taotian_fim_2.jsonl", "file_name": "/data/oss_bucket_0/data/PT/0510/cutoff_length_dataset_part2.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-taotian-test": {"file_name": "/data/oss_bucket_0/data/PT/test_mtl.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-merged-fim11-1": {"file_name": "/data/oss_bucket_0/data/PT/pt_fim_11-1.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-merged-fim11-2": {"file_name": "/data/oss_bucket_0/data/PT/pt_fim_11-2.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "sft-codeRule-0313": {"file_name": "/data/oss_bucket_0/data/SFT/rule_match_0313.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "sft-android-google": {"file_name": "/data/oss_bucket_0/test/datasets-55zvj6g3NxJm-alpaca-2025-06-07.jsonl", "file_sha1": "android-google-test1"}, "sft-codeRule-0319": {"file_name": "/data/oss_bucket_0/data/SFT/rule_match_0319.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "sft-codeRule-0317": {"file_name": "/data/oss_bucket_0/data/SFT/rule_match_0317.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "sft-codeRule-0324": {"file_name": "/data/oss_bucket_0/data/SFT/rule_match_0324.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "sft-inner-fim-0518": {"file_name": "/data/oss_bucket_0/data/SFT/sft_processing.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "sft-inner-fim-0603": {"file_name": "/data/oss_bucket_0/data/SFT/fim_8k/sft_processing_8k_split/", "file_sha1": "规约生成的数据训练集中的负样本"}, "sft-outer-fim-0605": {"file_name": "/data/oss_bucket_0/data/SFT/fim_8k/opensource_fim_data.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "sft-outer2-fim-0518": {"file_name": "/data/oss_bucket_0/data/SFT/opensource_fim_data2.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "sft-cross-fim-0518": {"file_name": "/data/oss_bucket_0/data/SFT/cross_eval.jsonl", "file_sha1": "规约生成的数据训练集中的负样本"}, "pt-merged-fim-fixed": {"file_name": "/data/oss_bucket_0/data/PT/random.jsonl", "file_sha1": "增减了一些ntp+fim的数据", "columns": {"prompt": "text"}}, "pt-random-fim-keyword": {"file_name": "/data/oss_bucket_0/data/PT/random_fim_sft_keyword.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-block-fim-sft": {"file_name": "/data/oss_bucket_0/data/PT/block_fim_sft.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-opencoder-code": {"file_name": "/data/oss_bucket_0/data/PT/opencoder-code-pt.jsonl", "file_sha1": "OpenCoder的开源代码数据，分别是21-25/510", "columns": {"prompt": "text"}}, "pt-opencoder-math": {"file_name": "/data/oss_bucket_0/data/PT/opencoder-math-pt.jsonl", "file_sha1": "OpenCoder的开源数学数据，分别是10-14/370", "columns": {"prompt": "text"}}, "pt-shoutao": {"file_name": "/data/oss_bucket_0/data/PT/pt-shoutao.txt", "file_sha1": "手工手淘组件数据集收集", "columns": {"prompt": "text"}}, "pt-stack-swift": {"file_name": "/data/oss_bucket_0/data/PT/pt-stack_swift_format_1.json", "file_sha1": "647f4ad447bd993e4b6b6223d1be15208bab694a", "columns": {"prompt": "text"}}, "pt-iOS-kotlin": {"file_name": "/data/oss_bucket_0/data/PT/pt-ios-kotlin.json", "file_sha1": "ffe3ecb58ab642da33fbb514d5e6188f1469ad40", "columns": {"prompt": "rowcode"}}, "pt-MTL-code": {"file_name": "/data/oss_bucket_0/data/PT/cleaned_codeqwen2_format.json", "file_sha1": "清洗cleaned_code_*的格式数据", "columns": {"prompt": "training_data"}}, "pt-jb-kotlin": {"file_name": "/data/oss_bucket_0/data/PT/jetbrain_ktclean.jsonl", "file_sha1": "JetBrain构建的kotlin数据集，共计25k,数据质量通过其本身的判别模型审核", "columns": {"prompt": "content"}}, "pt-MTL-code-0": {"file_name": "/data/oss_bucket_0/data/PT/filtered_processed.jsonl", "file_sha1": "合并数据-将原来的0-3合并成当前的数据", "columns": {"prompt": "text"}}, "inner_knowledge_data": {"file_name": "/data/oss_bucket_0/data/PT/0518/processed_data_0518.jsonl", "file_sha1": "摩天轮数据飞轮上配置的文档数据", "columns": {"prompt": "text"}}, "inner_md_data": {"file_name": "/data/oss_bucket_0/data/PT/0518/inner_knowledge_0518.jsonl", "file_sha1": "通过手工收集的文章内容", "columns": {"prompt": "text"}}, "inner_md_data_sft": {"file_name": "/data/oss_bucket_0/data/PT/0518/processed_data_0518.jsonl", "file_sha1": "通过手工收集的文章内容", "columns": {"prompt": "text"}}, "inner_code_data": {"file_name": "/data/oss_bucket_0/data/PT/0518/inner_code_0518.jsonl", "file_sha1": "摩天轮数据飞轮上配置的代码数据", "columns": {"prompt": "text"}}, "pt-MTL-code-1": {"file_name": "/data/oss_bucket_0/data/PT/cleaned_code_1.jsonl", "file_sha1": "从MTL打包收集的内容，代码全部10W条数据-1", "columns": {"prompt": "text"}}, "pt-MTL-code-2": {"file_name": "/data/oss_bucket_0/data/PT/cleaned_code_2.jsonl", "file_sha1": "从MTL打包收集的内容，代码全部3w条数据-2", "columns": {"prompt": "text"}}, "pt-MTL-code-3": {"file_name": "/data/oss_bucket_0/data/PT/cleaned_code_3.jsonl", "file_sha1": "从MTL打包收集的内容，代码全部3w条数据-2", "columns": {"prompt": "text"}}, "pt-ast-fim": {"file_name": "/data/oss_bucket_0/data/PT/block_fim_pt.jsonl", "file_sha1": "从MTL打包收集的内容，代码全部3w条数据-2", "columns": {"prompt": "text"}}, "pt-shoutao-code": {"file_name": "/data/oss_bucket_0/data/PT/shoutao_code.json", "file_sha1": "从MTL采集的非重复代码，代码清洗，共计78K条，原始数据18G，清洗后获得400M", "columns": {"prompt": "raw_code"}}, "pt-android-kotlin": {"file_name": "/data/oss_bucket_0/data/PT/pt-android-kotlin.json", "columns": {"prompt": "rowcode"}}, "pt-pay-mini": {"file_name": "/data/oss_bucket_0/data/PT/mini-program-training.jsonl", "columns": {"prompt": "text"}}, "pt-pay-ios": {"file_name": "/data/oss_bucket_0/data/PT/filtered_raw_training_iOS_v3.jsonl", "columns": {"prompt": "text"}}, "pt-pay-android": {"file_name": "/data/oss_bucket_0/data/PT/filtered_raw_training_android_v3.jsonl", "columns": {"prompt": "text"}}, "pt-pay-harmony-ets": {"file_name": "/data/oss_bucket_0/data/PT/filtered_raw_training_harmony_ets_v3.jsonl", "columns": {"prompt": "text"}}, "pt-pay-harmony-c": {"file_name": "/data/oss_bucket_0/data/PT/filtered_raw_training_harmony_c_v3.jsonl", "columns": {"prompt": "text"}}, "pt-swift-docstring": {"file_name": "/data/oss_bucket_0/data/PT/pt-swift-docstring-gpt4.json", "file_sha1": "ffe3ecb58ab642da33fbb514d5e6188f1469ad40", "columns": {"prompt": "text"}}, "pt-java-docstring": {"file_name": "/data/oss_bucket_0/data/PT/pt-java-docstring.json", "file_sha1": "ffe3ecb58ab642da33fbb514d5e6188f1469ad40", "columns": {"prompt": "text"}}, "sharegpt4": {"hf_hub_url": "shibing624/sharegpt_gpt4", "ms_hub_url": "AI-ModelScope/sharegpt_gpt4", "formatting": "sharegpt"}, "ultrachat_200k": {"hf_hub_url": "HuggingFaceH4/ultrachat_200k", "ms_hub_url": "AI-ModelScope/ultrachat_200k", "columns": {"messages": "messages"}, "tags": {"role_tag": "role", "content_tag": "content", "user_tag": "user", "assistant_tag": "assistant"}, "formatting": "sharegpt"}, "agent_instruct": {"hf_hub_url": "THUDM/AgentInstruct", "ms_hub_url": "ZhipuAI/AgentInstruct", "formatting": "sharegpt"}, "lmsys_chat": {"hf_hub_url": "lmsys/lmsys-chat-1m", "ms_hub_url": "AI-ModelScope/lmsys-chat-1m", "columns": {"messages": "conversation"}, "tags": {"role_tag": "role", "content_tag": "content", "user_tag": "human", "assistant_tag": "assistant"}, "formatting": "sharegpt"}, "evol_instruct": {"hf_hub_url": "WizardLM/WizardLM_evol_instruct_V2_196k", "ms_hub_url": "AI-ModelScope/WizardLM_evol_instruct_V2_196k", "formatting": "sharegpt"}, "cosmopedia": {"hf_hub_url": "HuggingFaceTB/cosmopedia", "columns": {"prompt": "prompt", "response": "text"}}, "oasst_de": {"hf_hub_url": "mayflowergmbh/oasst_de"}, "dolly_15k_de": {"hf_hub_url": "mayflowergmbh/dolly-15k_de"}, "alpaca-gpt4_de": {"hf_hub_url": "mayflowergmbh/alpaca-gpt4_de"}, "openschnabeltier_de": {"hf_hub_url": "mayflowergmbh/openschna<PERSON><PERSON>_de"}, "evol_instruct_de": {"hf_hub_url": "mayflowergmbh/evol-instruct_de"}, "dolphin_de": {"hf_hub_url": "mayflowergmbh/dolphin_de"}, "booksum_de": {"hf_hub_url": "mayflowergmbh/booksum_de"}, "airoboros_de": {"hf_hub_url": "mayflowergmbh/airoboros-3.0_de"}, "ultrachat_de": {"hf_hub_url": "mayflowergmbh/ultra-chat_de"}, "hh_rlhf_en": {"script_url": "hh_rlhf_en", "columns": {"prompt": "instruction", "response": "output", "history": "history"}, "ranking": true}, "oaast_rm": {"file_name": "oaast_rm.json", "file_sha1": "622d420e9b70003b210618253bd3d9d2891d86cb", "columns": {"prompt": "instruction", "query": "input", "response": "output", "history": "history"}, "ranking": true}, "oaast_rm_zh": {"file_name": "oaast_rm_zh.json", "file_sha1": "1065af1f3784dd61be5e79713a35f427b713a232", "columns": {"prompt": "instruction", "query": "input", "response": "output", "history": "history"}, "ranking": true}, "comparison_gpt4_en": {"file_name": "comparison_gpt4_data_en.json", "file_sha1": "96fa18313544e22444fe20eead7754b17da452ae", "ranking": true}, "comparison_gpt4_zh": {"file_name": "comparison_gpt4_data_zh.json", "file_sha1": "515b18ed497199131ddcc1af950345c11dc5c7fd", "ranking": true}, "nectar_rm": {"hf_hub_url": "mlinmg/RLAIF-Nectar", "ms_hub_url": "AI-ModelScope/RLAIF-Nectar", "ranking": true}, "orca_dpo_de": {"hf_hub_url": "mayflowergmbh/intel_orca_dpo_pairs_de", "ranking": true}, "wiki_demo": {"file_name": "wiki_demo.txt", "file_sha1": "e70375e28eda542a90c68213640cc371898ce181", "columns": {"prompt": "text"}}, "c4_demo": {"file_name": "c4_demo.json", "file_sha1": "a5a0c86759732f9a5238e447fecd74f28a66cca8", "columns": {"prompt": "text"}}, "dpo_zh_demo": {"file_name": "/data/oss_bucket_0/data/DPO/dpo_test.json", "file_sha1": "515b18ed497199131ddcc1af950345c11dc5c7fd", "ranking": true}, "refinedweb": {"hf_hub_url": "tiiuae/falcon-refinedweb", "columns": {"prompt": "content"}}, "redpajama_v2": {"hf_hub_url": "togethercomputer/RedPajama-Data-V2", "columns": {"prompt": "raw_content"}, "subset": "default"}, "wikipedia_en": {"hf_hub_url": "olm/olm-wikipedia-20221220", "ms_hub_url": "AI-ModelScope/olm-wikipedia-20221220", "columns": {"prompt": "text"}}, "wikipedia_zh": {"hf_hub_url": "pleisto/wikipedia-cn-20230720-filtered.json", "columns": {"prompt": "completion"}}, "wikipedia_zh_bak": {"hf_hub_url": "pleisto/wikipedia-cn-20230720-filtered", "ms_hub_url": "AI-ModelScope/wikipedia-cn-20230720-filtered", "file_name": "comparison_gpt4_data_zh.json", "columns": {"prompt": "completion"}}, "pile": {"hf_hub_url": "EleutherAI/pile", "ms_hub_url": "AI-ModelScope/pile", "columns": {"prompt": "text"}, "subset": "all"}, "skypile": {"hf_hub_url": "Skywork/SkyPile-150B", "ms_hub_url": "AI-ModelScope/SkyPile-150B", "columns": {"prompt": "text"}}, "the_stack_swift": {"hf_hub_url": "bigcode/the-stack-v2", "ms_hub_url": "AI-ModelScope/the-stack-v2", "columns": {"prompt": "content"}, "folder": "Swift"}, "the_stack_objc": {"hf_hub_url": "bigcode/the-stack-v2", "ms_hub_url": "AI-ModelScope/the-stack-v2", "columns": {"prompt": "content"}, "folder": "Objective-C"}, "the_stack_kotlin": {"hf_hub_url": "bigcode/the-stack-v2", "ms_hub_url": "AI-ModelScope/the-stack-v2", "columns": {"prompt": "content"}, "folder": "<PERSON><PERSON><PERSON>"}, "the_stack_java": {"hf_hub_url": "bigcode/the-stack-v2", "ms_hub_url": "AI-ModelScope/the-stack-v2", "columns": {"prompt": "content"}, "folder": "Java"}, "the_stack_cpp": {"hf_hub_url": "bigcode/the-stack-v2", "ms_hub_url": "AI-ModelScope/the-stack-v2", "columns": {"prompt": "content"}, "folder": "C++"}, "starcoder_python": {"hf_hub_url": "bigcode/starcoderdata", "ms_hub_url": "AI-ModelScope/starcoderdata", "columns": {"prompt": "content"}, "folder": "python"}}