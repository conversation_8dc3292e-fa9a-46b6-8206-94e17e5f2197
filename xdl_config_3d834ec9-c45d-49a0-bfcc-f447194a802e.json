{"job_name": "/data/xpfs_0/data/model/Qwen2.5-Coder-7B/Qwen2.5-Coder-7B/-2025-04-30-15-21-38", "training_type": "pytorch", "docker_image": "reg.docker.alibaba-inc.com/alimama/xdl:pytorch_alpha", "script": "", "worker": {"instance_num": 1, "cpu_cores": 8.0, "gpu_cores": 1.0, "memory_m": 80000}, "ps": {"instance_num": 0, "cpu_cores": 4, "gpu_cores": 1, "memory_m": 40000}, "reader": {"data_dir": "./data"}, "checkpoint": {"output_dir": "./ckpt"}, "meta_dir": "./meta", "parameter": {"worker": {"user_define_cmd": "src/train_bash.py --stage pt     --model_name_or_path=/data/xpfs_0/data/model/Qwen2.5-Coder-7B/Qwen2.5-Coder-7B/     --do_train     --do_eval      --val_size=0.005     --eval_steps 50     --dataset=pt-c-fim-1,pt-c-fim-2,pt-java-fim-1,pt-java-fim-2,pt-java-fim-3,pt-java-fim-4,pt-java-fim-5,pt-swift-fim-1,pt-swift-fim-2,pt-swift-fim-3,pt-swift-fim-4,pt-swift-fim-5,pt-taotian-fim2,pt-taotian-fim1,pt-kotlin-fim    --template default     --prompt=text #    --tokenized_path /data/xpfs_0/data/PT/Token/     --finetuning_type full     --output_dir=/data/xpfs_0/data/model/code_completion_1.5/     --overwrite_cache     --per_device_eval_batch_size 1     --per_device_train_batch_size 8     --gradient_accumulation_steps 2     --lr_scheduler_type cosine     --logging_steps 10     --save_steps 500     --num_train_epochs 1     --learning_rate=3e-5     --warmup_ratio 0.1     --cutoff_len=4096     --packing False     --preprocessing_num_workers=64     --dataloader_num_workers=4     --plot_loss     --do_sample     --deepspeed=scripts/ds_zero3_cpuoffload.json     --bf16"}}, "min_finish_worker_rate": 100, "oss_access_id": "LTAIGcY7idsIs0lm", "oss_access_key": "KM7z2PawYxDLqZWIFk7VraBUlZyBLk", "oss_bucket": "mtl4-ai-resources-daily", "job_success_notice": "true", "oss_endpoint": "oss-cn-hangzhou-internal.aliyuncs.com", "algo_name": "pytorch220", "scheduler_queue": "taobao_dev_mtl"}