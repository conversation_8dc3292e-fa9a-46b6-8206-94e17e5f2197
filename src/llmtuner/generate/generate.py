import torch
from tqdm import tqdm
from transformers import set_seed
import asyncio

from ..extras.misc import log_meta_to_openlm
from .engine import create_generate_engine
from .infer_args import get_generate_args
from .io import create_dataloader_and_writer
from .engine import VllmAsyncGenerateEngine, VllmAsyncMultiNodeGenerateEngine
from .dataloader import EIPDataloader


def run():
    model_args, finetuning_args, generating_args, infer_args = get_generate_args()
    infer_engine = create_generate_engine(model_args, infer_args, finetuning_args, generating_args)
    log_meta_to_openlm(finetuning_args=finetuning_args,infer_args=infer_args)
    dataloader, writer = create_dataloader_and_writer(infer_args,
                                                      infer_engine.get_tokenizer(),
                                                      infer_engine.get_template(),
                                                      infer_engine.get_processor(),
                                                      infer_engine.get_model_dtype(),
                                                      infer_engine.get_num_return(),
                                                      infer_engine.get_output_type())
    set_seed(infer_args.seed)
    try:
        predict(dataloader, writer, infer_engine)
    finally:
        writer.close()


async def producer(dataloader, writer, input_queue):
    PRODUCER_PUT_TIMEOUT = 15 * 60
    for index, data in enumerate(tqdm(dataloader, desc="Generating")):
        if isinstance(dataloader, EIPDataloader):
            slice_info, source_data = data
            if source_data:
                source_data['slice_info'] = slice_info
                data = source_data
            else:
                writer.commit_slice(slice_info)
                continue
        if not data:
            continue

        await asyncio.wait_for(input_queue.put(data), timeout=PRODUCER_PUT_TIMEOUT)


async def pusher(writer, output_queue):
     while True:
        data = await output_queue.get()
        output_queue.task_done()
        if data is None:
            break
        output_raw_data, output_data = data
        if output_raw_data['samples']:
            writer.write(output_raw_data, output_data)

async def consumer(infer_engine, input_queue, output_queue, consumer_id):
    while True:
        data = await input_queue.get()
        input_queue.task_done()
        if data is None:
            break
        processed_item = await infer_engine.predict_batch(data)
        await output_queue.put(processed_item)


async def predict_in_asyncio(dataloader, writer, infer_engine):
    VLLM_PARALLELISM_WORKER_CNT = 128
    PRODUCER_BUFFER_SIZE = 40
    input_queue = asyncio.Queue(maxsize=PRODUCER_BUFFER_SIZE)
    output_queue = asyncio.Queue()

    producer_task = asyncio.create_task(producer(dataloader, writer, input_queue))
    pusher_task = asyncio.create_task(pusher(writer, output_queue))
    consumers = [asyncio.create_task(consumer(infer_engine, input_queue, output_queue, consumer_id)) for consumer_id in range(VLLM_PARALLELISM_WORKER_CNT)]
    
    await producer_task
    for _ in range(len(consumers)):
        await input_queue.put(None)
    
    await asyncio.gather(*consumers)

    await output_queue.put(None)
    await pusher_task


def predict_in_sync_mode(dataloader, writer, infer_engine):
    for index, data in enumerate(tqdm(dataloader, desc="Generating")):
        if not data:
            continue
        output_data = infer_engine.predict_batch(data)
        writer.write(data, output_data)


def predict_in_async_mode(dataloader, writer, infer_engine):
    iter_instance = enumerate(tqdm(dataloader, desc="Generating"))
    continue_flag = True
    while continue_flag:
        try:
            index, data = next(iter_instance)
            if isinstance(dataloader, EIPDataloader):
                slice_info, source_data = data
                if source_data:
                    source_data['samples'].append(slice_info)
                    data = source_data
                else:
                    # batch内有异常数据导致batch预处理失败返回None，为保证slice完整提交，所以直接提交该片slice
                    writer.commit_slice(slice_info)
                    continue
            if data is None:
                continue
            output_raw_data, output_data = infer_engine.predict_batch(data)
        except StopIteration:
            continue_flag = False
            output_raw_data, output_data = infer_engine.collect_all_request()

        if output_raw_data['samples']:
            writer.write(output_raw_data, output_data)


@torch.inference_mode()
def predict(dataloader, writer, infer_engine):
    if isinstance(infer_engine, VllmAsyncGenerateEngine):
        predict_in_async_mode(dataloader, writer, infer_engine)
    elif isinstance(infer_engine, VllmAsyncMultiNodeGenerateEngine):
        asyncio.run(predict_in_asyncio(dataloader, writer, infer_engine))
    else:
        predict_in_sync_mode(dataloader, writer, infer_engine)
