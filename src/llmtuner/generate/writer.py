from typing import Dict, List, Any, Iterator
from datetime import datetime
import os
import json

from ..data import create_odps_table
from ..extras.logging import get_logger
from .eip import EIPSingleInstance
from .constant import OutputType
from .utils import is_external_cluster

logger = get_logger(__name__)


class FileWriter(object):
    def __init__(self, output):
        output_dir = os.path.dirname(output)
        os.makedirs(output_dir, exist_ok=True)
        if int(os.getenv('WORLD_SIZE', '1')) > 1:
            output += f"_{os.getenv('RANK', '0')}"
            logger.info(f"outputs_file is changed to {output}")

        self.output_file = open(output, "w", encoding="utf-8")

    def sample_iterator(self, samples: Dict[str, List[Any]]) -> Iterator[Dict[str, Any]]:
        # 获取batch数据每列的元素个数
        column_item_cnt = dict(zip(samples.keys(), list(map(len, list(samples.values())))))
        assert len(set(column_item_cnt.values())) == 1, \
            f"The data lengths of each column do not correspond, got {column_item_cnt}"
        for sample in zip(*samples.values()):
            yield dict(zip(samples.keys(), sample))

    def write(self, data, output_data):
        data["samples"]["predict"] = output_data.predict
        if output_data.generated_tokens_with_probs:
            data["samples"]["tokens_probs"] = output_data.generated_tokens_with_probs
        if output_data.audio_path is not None:
            data["samples"]["audio"] = output_data.audio_path
        for sample in self.sample_iterator(data["samples"]):
            # 如果预测为None,就不写出
            if sample['predict'] is not None:
                self.output_file.write(json.dumps(sample, ensure_ascii=False) + "\n")

    def close(self):
        self.output_file.close()


class EIPWriter(object):
    def __init__(self, inputs, outputs, input_columns, num_return, output_type):
        if is_external_cluster():
            logger.info("is_external_cluster true, skip create_odps_table! Make sure you have created the input table and input partition ahead of time")
        else:
            create_odps_table(inputs, outputs, input_columns, num_return, output_type)
        self.output_scores = output_type == OutputType.TokensProbabilities
        self.writer = EIPSingleInstance()

    def write(self, data, output):
        samples = data['samples']
        slice_info = data.get('slice_info', None)
        output_data = output.predict
        # transpose samples and append responses
        if self.output_scores:
            generated_tokens_with_probs = output.generated_tokens_with_probs
            data = [list(subsample) + [output_data[i]] + [json.dumps(generated_tokens_with_probs[i], ensure_ascii=False)]
                       for i, subsample in enumerate(zip(*samples)) if output_data[i] is not None]
        else:
            data = [list(subsample) + [output_data[i]]
                    for i, subsample in enumerate(zip(*samples)) if output_data[i] is not None]
        if slice_info:
            logger.info(f'{datetime.now()} [EIPWriter]:trace data length:{len(data)}')
            self.writer.trace(data, slice_info)
        else:
            self.writer.trace(data)
    
    def commit_slice(self, slice_info):
        self.writer.trace(data=None, slice_info=slice_info)

    def close(self):
        self.writer.close()


class OdpsTunnelWriter(object):
    def __init__(self, outputs):
        if outputs.startswith("odps://"):
            import common_io
            table = outputs
            writer = common_io.table_tunnel.TableWriter(table, slice_id=0)
            self._table_schema = writer._writer_session.schema
            odps_cols = self._table_schema.get_columns()
            self._odps_col_names = [col.name for col in odps_cols]
            logger.info(f"{table} col_names: {self._odps_col_names}")
            self._col_indices = [i for i in range(len(odps_cols))]
            try:
                self.writer = writer
            finally:
                writer.close()
        else:
            raise NotImplementedError

    def write(self, data):
        self.writer.write(data, self._col_indices)

    def close(self):
        self.writer.close()
