import itertools
from datetime import datetime
from enum import Enum, unique
from typing import TYPE_CHECKING
import asyncio
import os


import numpy as np
import torch

from ..data.template import get_template_and_fix_tokenizer
from ..extras.logging import get_logger
from ..extras.misc import down_model, to_device
from ..extras.packages import is_vllm_version_greater_than
from ..model.loader import load_model_and_template, load_model_config, load_tokenizer
from .constant import OutputType
from .data import OutputDataAfterEngine
from .ray_tool import ray_cluster_init_local, ray_cluster_init_remote


if TYPE_CHECKING:
    from .infer_args import InferArguments

logger = get_logger(__name__)


def change_dict_to_list(data_dict):
    if not data_dict:
        return []
    column_length_list = [len(values) for key, values in data_dict.items()]
    assert len(set(column_length_list)) == 1, \
        f"data length not same! get length {column_length_list}"

    data_list_of_dicts = [dict(zip(data_dict.keys(), row)) for row in zip(*data_dict.values())]
    return data_list_of_dicts


def change_list_to_dict(data_list):
    if not data_list:
        return {}
    column_length_list = [len(column) for column in data_list]
    assert len(set(column_length_list)) == 1,\
        f"data length not same! get length {column_length_list}"

    keys = data_list[0].keys()
    data_list_of_dicts = {k: [d.get(k) for d in data_list] for k in keys}
    return data_list_of_dicts


def model_args_refine(model_args):
    model_args.device_map = "balanced"
    model_args.model_name_or_path = down_model(model_args.model_name_or_path, used_for="model_name_or_path")
    if model_args.adapter_name_or_path is not None:
        model_args.adapter_name_or_path = [down_model(item,used_for="adapter_name_or_path") for item in model_args.adapter_name_or_path]
    return model_args


def change_column_to_row(data_list):
    if not data_list:
        return data_list
    column_length_list = [len(column) for column in data_list]
    assert len(set(column_length_list)) == 1,\
        f"data length not same! get length {column_length_list}"
    return list(map(list, zip(*data_list)))

def run_coroutine(coroutine):
    loop = asyncio.get_event_loop()
    task = loop.create_task(coroutine)
    return loop.run_until_complete(task)

@unique
class CallMode(str, Enum):
    OFFLINE_INFERENCE = 'offline_inference'
    CHAT_API = 'chat_api'
class BaseGenerateEngine(object):

    def __init__(self, model, tokenizer, processor, config, template, generating_args, infer_args, call_mode = CallMode.OFFLINE_INFERENCE):
        self.model = model
        self.tokenizer = tokenizer
        self.processor = processor
        self.config = config
        self.template = template
        self.gen_kwargs = generating_args.to_dict()
        self.infer_args = infer_args
        self.print_debug_log = True

        self.num_return = None

        self.fail_input_ids_index = []
        self.gen_kwargs_refine()

        self.output_type = self.confirm_output_type(config, self.gen_kwargs)

        self.tokenizer_refine()
        self.call_mode = call_mode
        if not self.call_mode:
            self.call_mode = CallMode.OFFLINE_INFERENCE

    def confirm_output_type(self, config, generating_args):
        return OutputType.Sequence

    def get_call_mode(self):
        return self.call_mode

    def get_num_return(self):
        return self.num_return

    def get_processor(self):
        return self.processor

    def get_template(self):
        return self.template

    def get_tokenizer(self):
        return self.tokenizer

    def get_model_dtype(self):
        raise Exception('BaseGenerateEngine do not impl get_model_dtype')

    def get_output_type(self):
        return self.output_type

    def tokenizer_refine(self):
        if self.output_type in (OutputType.Sequence, OutputType.TokensProbabilities):
            self.tokenizer.padding_side = "left"  # restore padding side
            self.tokenizer.init_kwargs["padding_side"] = "left"
        else:
            self.tokenizer.padding_side = "right"
            self.tokenizer.init_kwargs["padding_side"] = "right"

    def gen_kwargs_refine(self):
        self.num_return = self.gen_kwargs["num_return_sequences"] if "num_return_sequences" in self.gen_kwargs else 1

        # config token id after get_template_and_fix_tokenizer
        self.gen_kwargs["eos_token_id"] = [self.tokenizer.eos_token_id] + self.tokenizer.additional_special_tokens_ids
        self.gen_kwargs["pad_token_id"] = self.tokenizer.pad_token_id
        logger.info(f"after refine gen_kwargs: {self.gen_kwargs}")

    def predict_batch_impl(self, samples):
        logger.warning('BaseGenerateEngine not be inherit!')
        return samples

    def on_predict_batch_begin(self, samples):
        self.fail_input_ids_index = samples.pop('fail_input_ids_index', [])
        return samples

    def on_predict_batch_finish(self, outputs_after_engine):
        outputs = outputs_after_engine.predict
        if self.num_return > 1:
            outputs = [outputs[i:i + self.num_return] for i in range(0, len(outputs), self.num_return)]
        # 如果有失败数据索引，则将失败数据output设为None
        if self.fail_input_ids_index:
            for _index in self.fail_input_ids_index:
                outputs[_index] = None
        outputs_after_engine.predict = outputs
        return outputs_after_engine

    def predict_batch(self, samples):
        samples = self.on_predict_batch_begin(samples)
        outputs_after_engine = self.predict_batch_impl(samples)
        outputs_after_engine = self.on_predict_batch_finish(outputs_after_engine)
        return outputs_after_engine


class HuggingFaceGenerateEngine(BaseGenerateEngine):
    def __init__(self, model_args, infer_args: "InferArguments", finetuning_args, generating_args, call_model):
        model_args = model_args_refine(model_args)
        model_args.compute_dtype = infer_args.compute_dtype
        model, template = load_model_and_template(
            model_args, finetuning_args, template=infer_args.template, is_trainable=False
        )
        tokenizer, processor = template.tokenizer, template.processor
        config = model.config
        if hasattr(model, "config") and hasattr(model.config, "use_cache"):
            model.config.use_cache = True
        if hasattr(model, "generation_config") and hasattr(model.generation_config, "use_cache"):
            model.generation_config.use_cache = True

        super().__init__(model, tokenizer, processor, config, template, generating_args, infer_args, call_model)

        if self.output_type == OutputType.Embedding:
            logger.info('current model is emb_model, init embedding generate environment ...')
            self.emb_pooling_method = config.emb_pooling_method
            self.predict_batch_impl = self.predict_batch_impl_for_emb
        elif self.output_type == OutputType.RewardScores:
            logger.info('current model is rm model, init reward scores generate environment ...')
            self.predict_batch_impl = self.predict_batch_impl_for_reward_scores
        elif self.output_type == OutputType.Audio:
            logger.info('current model is tts model, init audio generate environment ...')
            model.init_tts()
            self.gen_kwargs["audio_output_path"] = infer_args.outputs

    def confirm_output_type(self, config, gen_kwargs):
        output_scores = False
        output_embedding = False
        output_reward_scores = False
        output_audios = False

        if self.infer_args.output_scores:
            output_scores = True
        if hasattr(config, "is_embedding_model"):
            output_embedding = True
        if hasattr(config, "is_rm_model"):
            output_reward_scores = True
        if self.infer_args.output_audio:
            output_audios = True

        output_type_flag = sum([output_scores, output_embedding, output_reward_scores, output_audios])
        assert output_type_flag <= 1, f"only support only one special output type, current " \
                                     f"output_scores:{output_scores}, " \
                                     f"output_embedding:{output_embedding}, " \
                                     f"output_reward_scores:{output_reward_scores} " \
                                     f"output_audios:{output_audios}"
        if output_scores:
            return OutputType.TokensProbabilities
        elif output_embedding:
            return OutputType.Embedding
        elif output_reward_scores:
            return OutputType.RewardScores
        elif output_audios:
            return OutputType.Audio
        else:
            return OutputType.Sequence

    def get_model_dtype(self):
         return self.model.dtype

    def print_debug_info(self, prompts, input_ids, output_ids, outputs):
        if not self.print_debug_log:
            return
        if isinstance(input_ids, torch.Tensor):
            input_ids = input_ids.tolist()
        if isinstance(output_ids, torch.Tensor):
            output_ids = output_ids.tolist()
        print("input:\n{}\n".format(prompts[0]))
        print("prompt:\n{}\n".format(self.tokenizer.decode(input_ids[0], skip_special_tokens=False)))
        print("input_ids:\n{}\n".format(input_ids[0]))
        print("output:\n{}\n".format(outputs[0]))
        print("output_ids:\n{}\n".format(output_ids[0]))
        if self.call_mode == CallMode.CHAT_API:
            self.print_debug_log = True
        else:
            self.print_debug_log = False

    def predict_batch_impl_for_reward_scores(self, samples):
        samples.pop("prompt_column", None)
        inputs = to_device(samples, self.model.device)
        inputs.pop("samples", None)  # original text samples
        outputs = self.model(**inputs)
        scores_list = outputs[0].squeeze(1).tolist()
        outputs = OutputDataAfterEngine(predict=scores_list)
        return outputs

    def predict_batch_impl_for_emb(self, samples):

        samples.pop("prompt_column", None)
        inputs = to_device(samples, self.model.device)
        inputs.pop("samples", None)  # original text samples
        attention_mask = inputs['attention_mask']
        outputs = self.model(**inputs)

        if self.emb_pooling_method == 'CLS':
            output_embeddings = outputs[0][:, 0]
        elif self.emb_pooling_method == 'LAST':
            sequence_lengths = attention_mask.sum(dim=1) - 1
            last_hidden_states = outputs[0]
            batch_size = last_hidden_states.shape[0]
            output_embeddings = last_hidden_states[
                torch.arange(batch_size, device=last_hidden_states.device), sequence_lengths]
        else:
            raise Exception('Unknown pooling method')
        # normalize embeddings
        output_embeddings = torch.nn.functional.normalize(output_embeddings, p=2, dim=1)
        outputs = OutputDataAfterEngine(predict=output_embeddings.tolist())
        return outputs

    def predict_batch_impl(self, samples):
        prompt_column = samples.pop("prompt_column")
        input_ids = samples['input_ids']
        pad_id = self.tokenizer.pad_token_id
        max_len = max(map(len, input_ids))

        attention_mask = [[0] * (max_len - len(input_id)) + [1] * len(input_id) for input_id in input_ids]
        input_ids = [[pad_id] * (max_len - len(input_id)) + input_id for input_id in input_ids]
        input_ids = torch.tensor(input_ids)
        attention_mask = torch.tensor(attention_mask)
        samples['attention_mask'] = attention_mask
        samples['input_ids'] = input_ids

        generated_tokens_with_probs = []
        inputs = to_device(samples, self.model.device)

        inputs.pop("samples", None)  # original text samples
        outputs = self.model.generate(
            return_dict_in_generate=True,
            **inputs,
            **self.gen_kwargs,
        )
        if self.output_type == OutputType.TokensProbabilities:
            transition_scores = self.model.compute_transition_scores(
                outputs.sequences, outputs.scores, normalize_logits=True)
            input_length = inputs['input_ids'].shape[1]
            generated_tokens = outputs.sequences[:, input_length:]
            for generated_token, transition_score in zip(generated_tokens, transition_scores):
                generated_tokens_with_prob = []
                for token, score in zip(generated_token, transition_score):
                    token = int(token.cpu())
                    if token == self.tokenizer.pad_token_id:
                        continue
                    generated_tokens_with_prob.append({
                        "id": str(token),
                        "text": self.tokenizer.decode(token),
                        "prob": str(np.round(np.exp(score.cpu().numpy()), 6)),
                    })
                generated_tokens_with_probs.append(generated_tokens_with_prob)

        multimodal_output_path = getattr(outputs, "multimodal_output_path", None)
        has_input_id_as_prefix = lambda input_ids, output_ids: (len(output_ids) > len(input_ids)) \
            and (all(input_id == output_id for input_id, output_id in zip(input_ids, output_ids))) 
        output_end_index = lambda output, start_index=0: output[start_index:].index(self.tokenizer.eos_token_id) + start_index \
            if self.tokenizer.eos_token_id in output[start_index:] else None
        truncate_output = lambda output, start_index=0: output[start_index:output_end_index(output, start_index)]
        output_ids = outputs["sequences"].tolist()
        output_tensor_ids = outputs["sequences"]
        output_finish_reasons = []
        output_lengths = []
        outputs = [
            self.tokenizer.decode(
                truncate_output(output, len(input_ids[j // self.num_return])) 
                if has_input_id_as_prefix(input_ids[j // self.num_return], output) else truncate_output(output),
                skip_special_tokens=True,
                clean_up_tokenization_spaces=False,
            )
            for j, output in enumerate(output_ids)
        ]
        if self.call_mode == CallMode.CHAT_API:
            for i in range(len(outputs)): 
                eos_index = (output_tensor_ids[i] == self.tokenizer.eos_token_id).nonzero()
                response_length = (eos_index[-1].item() + 1) if len(eos_index) else len(output_tensor_ids[i])
                output_lengths.append(response_length)
                output_finish_reasons.append("stop" if len(eos_index) else "length")

        self.print_debug_info(samples["samples"][prompt_column], input_ids, output_ids, outputs)

        outputs = OutputDataAfterEngine(
            predict=outputs,
            generated_tokens_with_probs=generated_tokens_with_probs,
            predict_finish_reasons=output_finish_reasons,
            predict_response_lengths=output_lengths,
            audio_path=multimodal_output_path
        )
        return outputs


class VllmGenerateEngine(BaseGenerateEngine):
    def __init__(self, model_args, infer_args: "InferArguments", generating_args, call_mode):
        model_args = model_args_refine(model_args)

        tokenizer, processor = load_tokenizer(model_args)
        template = get_template_and_fix_tokenizer(
            tokenizer, name=infer_args.template, processor=processor, processor_args=model_args
        )
        config = load_model_config(tokenizer=tokenizer, model_args=model_args, is_trainable=False)
        llm_args = {
                'model': model_args.model_name_or_path,
                'gpu_memory_utilization': infer_args.gpu_memory_utilization,
                'tensor_parallel_size': infer_args.tensor_parallel_size,
                'pipeline_parallel_size': infer_args.pipeline_parallel_size,
                "max_num_seqs": infer_args.vllm_max_num_seqs,
                'trust_remote_code': True,
                'enable_chunked_prefill': False,
                'block_size': infer_args.block_size,
                'max_model_len': infer_args.max_model_len,
                'enable_lora': True if model_args.adapter_name_or_path else False,
                'max_lora_rank': infer_args.max_lora_rank
            }
        if not is_vllm_version_greater_than("0.7.0"):
            llm_args.update(
                {
                    'worker_use_ray': True,
                }
            )
        if is_vllm_version_greater_than("0.6.1"):
            llm_args.update(
                {
                    'disable_custom_all_reduce': infer_args.disable_custom_all_reduce,
                    'max_seq_len_to_capture': infer_args.max_seq_len_to_capture,
                    'num_scheduler_steps': infer_args.num_scheduler_steps,
                    'enable_prefix_caching': infer_args.enable_prefix_caching,
                }
            )
        if is_vllm_version_greater_than("0.7.2"):
            llm_args['distributed_executor_backend'] = 'ray'
            llm_args['device'] = 'cuda'

        if getattr(config, "visual", None) or getattr(config, "vision_config", None):
            if is_vllm_version_greater_than("0.7.2"):
                llm_args['limit_mm_per_prompt'] = {"image": infer_args.limit_image_per_prompt}
            else:
                if getattr(config, "model_type", None) == "qwen2_vl":
                    rope_scaling={
                        "type": "mrope",
                        "mrope_section": [16, 24, 24],
                    }
                    llm_args["rope_scaling"] = rope_scaling
                llm_args['enforce_eager'] = True

        model = self.create_model(llm_args)

        self.model_args = model_args
        self.vllm_sampling_params = None
        self.vllm_lora_request = None
        super().__init__(model, tokenizer, processor, config, template, generating_args, infer_args, call_mode)

    def confirm_output_type(self, config, gen_kwargs):
        if self.infer_args.output_scores:
            return OutputType.TokensProbabilities
        if hasattr(config, "is_embedding_model"):
            raise Exception(f"{self.__class__.__name__} do not support output_type:{OutputType.Embedding}")
        if hasattr(config, "is_rm_model"):
            raise Exception(f"{self.__class__.__name__} do not support output_type:{OutputType.RewardScores}")
        return OutputType.Sequence

    def create_model(self, args):
        from vllm import LLM
        return LLM(**args)

    def get_model_dtype(self):
        return self.model.llm_engine.model_config.dtype

    def create_lora_request_for_vllm(self, adapter_name_or_path):
        from vllm.lora.request import LoRARequest
        assert len(adapter_name_or_path) == 1, "only support single lora adapter"
        return LoRARequest("adapter", 1, adapter_name_or_path[0])

    def create_sampling_params_for_vllm(self):
        from vllm import SamplingParams

        if not self.gen_kwargs["do_sample"]:
            self.gen_kwargs["temperature"] = 0.0

        basis_sampling_params = {
            'max_tokens': self.gen_kwargs["max_new_tokens"],
            'stop_token_ids': self.gen_kwargs["eos_token_id"],
            'repetition_penalty': self.gen_kwargs["repetition_penalty"],
            'n': self.gen_kwargs["num_return_sequences"],
        }

        if self.gen_kwargs["num_beams"] > 1:
            beams_sampling_params = {
                'best_of': self.gen_kwargs["num_beams"],
                'use_beam_search': True,
                'temperature': 0.0
            }
            basis_sampling_params.update(beams_sampling_params)
        else:
            do_sample_sampling_params = {
                'temperature': self.gen_kwargs["temperature"],
                'top_p': self.gen_kwargs["top_p"],
                'top_k': self.gen_kwargs["top_k"]
            }
            basis_sampling_params.update(do_sample_sampling_params)

        if self.output_type == OutputType.TokensProbabilities:
            basis_sampling_params['logprobs'] = 1

        if 'stop' in self.gen_kwargs:
            basis_sampling_params['stop'] = self.gen_kwargs['stop']
            # 需要移除，否则会覆盖。
            basis_sampling_params.pop('stop_token_ids')

        return SamplingParams(**basis_sampling_params)

    def parse_logprob_from_vllm_output(self, vllm_outputs):
        prob_list = []
        for j, row_outputs in enumerate(vllm_outputs):
            for jj, single_outputs in enumerate(row_outputs.outputs):
                row_prob_list = []
                for logprob_items in single_outputs.logprobs:
                    cur_logprob_item_key = list(logprob_items.keys())[0]
                    cur_logprob_item = logprob_items[cur_logprob_item_key]
                    row_prob_list.append({
                        "id": str(cur_logprob_item_key),
                        "text": cur_logprob_item.decoded_token,
                        "prob": cur_logprob_item.logprob,
                    })
                prob_list.append(row_prob_list)
        return prob_list

    def vllm_params_init(self):
        # 这个对于chat 场景，原实现如果已经初始化就不会再初始化。这个在chat中需要每次请求都修改配置的，是无法接受的。
        if self.call_mode == CallMode.CHAT_API:
            self.vllm_sampling_params = self.create_sampling_params_for_vllm()
            logger.info(f"sampling_params:{self.vllm_sampling_params}")
        else:
            # 对于离线批量推理，这里保持原逻辑，已经初始化之后不再初始化。
            if not self.vllm_sampling_params:
                self.vllm_sampling_params = self.create_sampling_params_for_vllm()
        if self.model_args.adapter_name_or_path and not self.vllm_lora_request:
            self.vllm_lora_request = self.create_lora_request_for_vllm(self.model_args.adapter_name_or_path)

    def predict_batch_impl(self, samples):
        self.vllm_params_init()
        input_ids = samples['input_ids']
        llm_inputs = [{"prompt_token_ids": prompt_token_ids} for prompt_token_ids in input_ids]
        if "multi_modal_data" in samples:
            assert len(llm_inputs) == 1, "only support batch_size=1 for multi_modal model"
            llm_inputs[0]["multi_modal_data"] = samples['multi_modal_data']
        vllm_outputs = self.model.generate(
            llm_inputs, sampling_params=self.vllm_sampling_params, lora_request=self.vllm_lora_request
        )
        outputs = self.vllm_output_post_processing(vllm_outputs)
        return outputs
    
    def vllm_output_post_processing(self, vllm_outputs, request_ids_for_outputs=None):
        generated_tokens_with_probs = self.parse_logprob_from_vllm_output(vllm_outputs=vllm_outputs) \
            if self.output_type == OutputType.TokensProbabilities else None
        # Print the outputs.
        outputs = [
            self.tokenizer.decode(
                output.token_ids,
                skip_special_tokens=True,
                clean_up_tokenization_spaces=False,
            )
            for j, outputs in enumerate(vllm_outputs) for output in outputs.outputs
        ]
        output_finish_reasons = []
        output_lengths = []
        if self.call_mode == CallMode.CHAT_API:
            logger.info(vllm_outputs)
            vllm_texts = []
            for j, outputs in enumerate(vllm_outputs):
                for output in outputs.outputs:
                    output_lengths.append(len(output.token_ids))
                    output_finish_reasons.append(output.finish_reason)
                    vllm_texts.append(output.text)
            outputs = vllm_texts
        outputs = OutputDataAfterEngine(predict=outputs,
                                        generated_tokens_with_probs=generated_tokens_with_probs,
                                        predict_finish_reasons=output_finish_reasons,
                                        predict_response_lengths=output_lengths,
                                        request_ids=request_ids_for_outputs)
        return outputs


class VllmAsyncGenerateEngine(VllmGenerateEngine):
    PENDING_SIZE = 1

    def __init__(self, model_args, infer_args, generating_args, call_mode):
        super().__init__(model_args, infer_args, generating_args, call_mode)
        self.id_creator = itertools.count(1)
        self.input_data_cache = {}
        # 用于存放失败数据的request_id
        self.fail_data_request_id_cache = set()
        # EIP方式需要提取slice信息，非EIP方式不用
        self.fetch_slice = infer_args.tables is not None and len(infer_args.tables) > 0
        self.stage = "warmup"
        self.max_gpu_cache_usage_sys = 0.8

    def get_model_dtype(self):
        return self.model.model_config.dtype

    def create_model(self, args):
        from vllm import EngineArgs, LLMEngine
        engine_args = EngineArgs(**args)
        return LLMEngine.from_engine_args(engine_args)

    def send_requests(self, input_ids, request_ids, multi_modal_data):
        assert len(input_ids) == len(request_ids), \
            f"len(input_ids):{len(input_ids)} and len(request_ids):{len(request_ids)} must be same"

        for prompt_ids in input_ids:
            req_id = request_ids.pop(0)
            llm_inputs = {
                "prompt_token_ids": prompt_ids,
            }
            if multi_modal_data:
                assert len(input_ids) == 1, "only support batch_size=1 for multi_modal model"
                llm_inputs["multi_modal_data"] = multi_modal_data
            self.model.add_request(request_id=req_id,
                                   inputs=llm_inputs,
                                   params=self.vllm_sampling_params,
                                   lora_request=self.vllm_lora_request,
                                   )
            logger.info(f'{datetime.now()} req_id:{req_id} add_request')

    def fetch_output(self):
        output_list = []
        request_outputs = self.model.step()
        for request_output in request_outputs:
            if request_output.finished:
                output_list.append([request_output.request_id, request_output])
                print(f'{datetime.now()} req_id:{request_output.request_id} finished')
        return output_list

    def has_unfinished_requests(self):
        return self.model.has_unfinished_requests()

    def wait_for_finish(self):
        all_output = []
        while self.has_unfinished_requests():
            all_output += self.fetch_output()
        return all_output

    def on_predict_batch_begin(self, samples):
        fail_input_ids_index = samples.pop('fail_input_ids_index', [])
        samples = super().on_predict_batch_begin(samples)
        # 缓存原始数据
        raw_samples = samples['samples']

        # 需要fetch_slice的场景，数据源是odps，raw_samples的格式是list
        if self.fetch_slice:
            raw_samples = change_column_to_row(raw_samples)
        else:
            raw_samples = change_dict_to_list(raw_samples)

        # 申请request_id
        request_ids = [str(next(self.id_creator)) for _ in range(len(raw_samples))]

        # 记录失败数据的request_id
        for fail_id in fail_input_ids_index:
            self.fail_data_request_id_cache.add(request_ids[fail_id])
        # 数据存放
        for i, cid in enumerate(request_ids):
            self.input_data_cache[cid] = raw_samples[i]

        # samples中放置request_ids，同时去掉slice_info
        samples['request_ids'] = request_ids

        return samples

    def collect_all_request(self):
        outputs_after_engine = self.predict_batch_impl(None)
        outputs_after_engine = self.on_predict_batch_finish(outputs_after_engine)
        return outputs_after_engine

    def on_predict_batch_finish(self, outputs_after_engine):
        outputs_after_engine = super().on_predict_batch_finish(outputs_after_engine)
        # 拉取原始数据
        output_raw_data = [self.input_data_cache[rid] for rid in outputs_after_engine.request_ids]

        # 将失败数据的predict设置会None,同时清理对应缓存
        for _index, rid in enumerate(outputs_after_engine.request_ids):
            if rid in self.fail_data_request_id_cache:
                outputs_after_engine.predict[_index] = ''
                self.fail_data_request_id_cache.remove(rid)

        if self.fetch_slice:
            output_raw_data = change_column_to_row(output_raw_data)
            slice_info = output_raw_data.pop(-1) if output_raw_data else []
            samples = {'samples': output_raw_data, 'slice_info': slice_info}
        else:
            output_raw_data = change_list_to_dict(output_raw_data)
            samples = {'samples': output_raw_data}
        # 清理缓存
        [self.input_data_cache.pop(key) for key in outputs_after_engine.request_ids]
        return samples, outputs_after_engine

    def wait_and_fetch_output(self):
        outputs = []
        while True:
            outputs += self.fetch_output()
            stats = self.model._get_stats(scheduler_outputs=None)
            processing_num = sum([stats.num_waiting_sys, stats.num_running_sys, stats.num_swapped_sys])
            if (
                stats.num_waiting_sys < self.PENDING_SIZE
                and stats.gpu_cache_usage_sys < self.max_gpu_cache_usage_sys
                and processing_num < self.model.get_scheduler_config().max_num_seqs
            ):
                if self.stage == "warmup":
                    self.stage = "stable"
                    self.max_gpu_cache_usage_sys = 0.99
                break
        return outputs

    def predict_batch_impl(self, samples):
        """
        async engine中，每次predict_batch执行两部操作
        第一步：取走引擎已经处理好的数据
        第二步：向引擎塞入本次step的batch数据
        存在另种特殊情况：
        第一：压数据时为了防止把pending buffer打爆，所以要不断check pending buffer大小，在check的死循环中，也可以从output buffer中看看有没有处理好的数据，有的话就拿走
        第二：最后一次step没数据压，而且因为是最后一次step，所以必须收走引擎所有的数据，所以添加了一个block的方法wait_for_finish，死等着一直收数据
        """
        self.vllm_params_init()
        origin_outputs = []
        if samples:
            request_ids = samples['request_ids']
            input_ids = samples['input_ids']
            mm_data = samples.get('multi_modal_data', None)
            origin_outputs += self.wait_and_fetch_output()
            self.send_requests(input_ids, request_ids, mm_data)
        else:
            origin_outputs = self.wait_for_finish()

        request_ids_for_outputs = [item[0] for item in origin_outputs]
        vllm_outputs = [item[1] for item in origin_outputs]
        outputs = self.vllm_output_post_processing(vllm_outputs, request_ids_for_outputs)
        return outputs


class VllmMultiNodeGenerateEngine(VllmGenerateEngine):
    def create_model(self, args):
        from vllm import AsyncLLMEngine,AsyncEngineArgs
        engine_args = AsyncEngineArgs(**args)
        self.id_creator = itertools.count(1)
        return AsyncLLMEngine.from_engine_args(engine_args)

    async def do_get_model_dtype(self, results_generator):
        config = await results_generator
        return config.dtype

    def get_model_dtype(self):
        get_model_config_generator = self.model.get_model_config()
        return run_coroutine(self.do_get_model_dtype(get_model_config_generator))

    async def do_generate_single(self, results_generator):
        async for request_output in results_generator:
            final_output = request_output
        return final_output
    
    async def do_generate_batch(self, results_generators):
        tasks = [self.do_generate_single(gen) for gen in results_generators]
        return await asyncio.gather(*tasks)

    def predict_batch_impl(self, samples):

        self.vllm_params_init()
        input_ids = samples['input_ids']

        vllm_outputs_generators = [self.model.generate(
            {"prompt_token_ids": input_id}, 
            sampling_params=self.vllm_sampling_params, 
            lora_request=self.vllm_lora_request,
            request_id=str(next(self.id_creator)),
        ) for input_id in input_ids]

        vllm_outputs = run_coroutine(self.do_generate_batch(vllm_outputs_generators))

        outputs = self.vllm_output_post_processing(vllm_outputs)
        return outputs


class VllmAsyncMultiNodeGenerateEngine(VllmMultiNodeGenerateEngine):

    async def predict_batch_impl(self, samples):

        self.vllm_params_init()
        input_ids = samples['input_ids']

        vllm_outputs_generator = self.model.generate(
            {"prompt_token_ids": input_ids[0]}, 
            sampling_params=self.vllm_sampling_params, 
            lora_request=self.vllm_lora_request,
            request_id=str(next(self.id_creator)),
        )

        vllm_outputs = await self.do_generate_single(vllm_outputs_generator)

        outputs = self.vllm_output_post_processing([vllm_outputs])
        return outputs
    
    async def predict_batch(self, samples):
        samples = self.on_predict_batch_begin(samples)
        outputs_after_engine = await self.predict_batch_impl(samples)
        outputs_after_engine = self.on_predict_batch_finish(outputs_after_engine)
        return samples, outputs_after_engine


class VllmEnvSetup(object):
    def __init__(self, model_args, infer_args):
        self.model_args = model_args
        self.infer_args = infer_args
    
    def __call__(self, *args, **kwds):
        model_args = model_args_refine(self.model_args)
        tokenizer, _ = load_tokenizer(model_args)
        load_model_config(tokenizer=tokenizer, model_args=model_args, is_trainable=False)


def create_generate_engine(model_args, infer_args, finetuning_args, generating_args, call_mode=CallMode.OFFLINE_INFERENCE):

    if 'vllm' in infer_args.infer_mode:
        if infer_args.infer_mode == 'vllm-multi-node':
            gpu_cnt_in_group = infer_args.tensor_parallel_size * infer_args.pipeline_parallel_size
            gpu_cnt_in_device = torch.cuda.device_count()
            assert gpu_cnt_in_group > gpu_cnt_in_device, "tensor_parallel_size * pipeline_parallel_size has to be greater than torch.cuda.device_count() in vllm-multi-node mode"
            assert gpu_cnt_in_group % gpu_cnt_in_device == 0, "tensor_parallel_size * pipeline_parallel_size must be a multiple of  torch.cuda.device_count()"
            node_size = gpu_cnt_in_group // gpu_cnt_in_device
            vllm_env_setup = VllmEnvSetup(model_args=model_args, infer_args=infer_args)
            ray_cluster_init_remote(model_args.model_name_or_path, node_size, vllm_env_setup)
            _rank = int(os.getenv("RANK", "0"))
            _world_size = int(os.getenv("WORLD_SIZE", "1"))
            assert _rank % node_size == 0
            assert _world_size % node_size == 0 
            os.environ['RANK'] = str(_rank // node_size)
            os.environ['WORLD_SIZE'] = str(_world_size // node_size)
        else:
            ray_cluster_init_local(infer_args)

    if infer_args.infer_mode == 'vllm':
        return VllmGenerateEngine(model_args, infer_args, generating_args, call_mode)
    if infer_args.infer_mode == 'vllm-async':
        assert call_mode == CallMode.OFFLINE_INFERENCE, "vllm-async only support OFFLINE_INFERENCE"
        return VllmAsyncGenerateEngine(model_args, infer_args, generating_args, call_mode)
    if infer_args.infer_mode == 'vllm-multi-node':
        assert call_mode == CallMode.OFFLINE_INFERENCE, "vllm-multi-node only support OFFLINE_INFERENCE"
        return VllmAsyncMultiNodeGenerateEngine(model_args, infer_args, generating_args, call_mode)
    else:
        return HuggingFaceGenerateEngine(model_args, infer_args, finetuning_args, generating_args, call_mode)
