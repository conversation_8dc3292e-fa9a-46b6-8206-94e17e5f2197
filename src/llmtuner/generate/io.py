import time
from .dataloader import <PERSON><PERSON><PERSON><PERSON>loader, FileDataloader
from .writer import <PERSON>IPWrite<PERSON>, FileWriter


def create_dataloader_and_writer(infer_args,
                                 tokenizer,
                                 template,
                                 processor=None,
                                 model_dtype=None,
                                 num_return=1,
                                 output_type=None):
    if infer_args.tables:
        input_columns = [infer_args.input_columns[_index] for _index in infer_args.ailake_non_multimodel_data_columns] if infer_args.inputs.startswith('ailake://') else infer_args.input_columns
        eip_writer = EIPWriter(infer_args.inputs, infer_args.outputs,
                               input_columns, num_return, output_type)
        eip_dataloader = EIPDataloader(infer_args, tokenizer, template, processor,
                                       model_dtype, output_type)
        return eip_dataloader, eip_writer
    elif infer_args.load_from == "file":
        return FileDataloader(infer_args, tokenizer, template, processor, model_dtype, output_type), \
               FileWriter(infer_args.outputs)
    elif infer_args.load_from == "openlm":
        raise NotImplementedError
    else:
        raise ValueError(f"Unknown load_from: {infer_args.load_from}")