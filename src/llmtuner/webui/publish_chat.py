import os
import signal
import socket
import threading
import time

from ..extras.logging import get_logger

logger = get_logger(__name__)


class PublishManager:

    def __init__(self, timeout_minute=24 * 60):
        self.route_key = os.environ.get('TASK_ID', None)
        self.timeout_minute = timeout_minute
        if not self.route_key:
            raise ValueError("can not find task_id in env")
        self.start_time = time.time()


    def get_ip(self):
        s = None
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
        finally:
            s.close()
        return ip

    def get_random_port(self):
        import random
        return random.randint(10000, 40000)

    def get_root_path(self):
        return f'/gateway/domain/mdl_chat/route/{self.route_key}/proxy/'

    def publish(self, port, mode='api'):
        ip = self.get_ip()
        import requests
        import json

        url = "https://nebula-proxy-gateway.alibaba-inc.com/gateway/register"

        payload = json.dumps({
            "domain": "mdl_chat",
            "routeKey": self.route_key,
            "originUrl": f"http://{ip}:{port}",
            "routeTimeoutMinutes": self.timeout_minute
        })
        headers = {
            'Content-Type': 'application/json',
        }

        logger.info({payload})
        response = requests.request("POST", url, headers=headers, data=payload)
        proxy_res = False
        if response.ok:
            body = response.json()
            if body['status'] == 0 and body['message']:
                proxy_res = True
                self.start_time = int(body['data']['firstTime']) / 1000

        if not proxy_res:
            raise RuntimeError(f"can not proxy this chat,because {response.text}")
        from datetime import datetime
        logger.info(f'register proxy success,first register at {datetime.fromtimestamp(self.start_time)}')
        if mode == 'api':
            print(f"You can access the API documentation at the link below to understand how to view inference results through the API.\n"
                        f"https://nebula-proxy-gateway.alibaba-inc.com{self.get_root_path()}docs")
            print(f"""You can use the following python code on your computer to view the inference results.\n
import requests
import json
url = "https://nebula-proxy-gateway.alibaba-inc.com{self.get_root_path()}v1/chat/completions"
payload = json.dumps({{
"model": "your model name",
   "messages": [
      {{
         "role": "user",
         "content": "推理的输入"
      }}
   ],
   "tools": [],
   "do_sample": True,
   "temperature": 0,
   "top_p": 0,
   "n": 1,
   "max_tokens": 0,
   "stream": False
}})
headers = {{
   'Content-Type': 'application/json',
   'Accept': '*/*',
   'Host': 'nebula-proxy-gateway.alibaba-inc.com',
   'Connection': 'keep-alive'
}}
response = requests.request("POST", url, headers=headers, data=payload)
print(response.text)\n""")

    def register_timeout_shutdown(self):

        def _shutdown():
            while True:
                time.sleep(60)
                current_time = time.time()
                if current_time - self.start_time > self.timeout_minute * 60:
                    logger.info(f'The service has exceeded {self.timeout_minute} minutes and it has shutdown.'
                                f'Please submit a new task to debug the model inference.')
                    # 触发uvicorn 优雅退出
                    os.kill(os.getpid(), signal.SIGINT)
                    break

        server_thread = threading.Thread(target=_shutdown, args=())
        server_thread.start()
