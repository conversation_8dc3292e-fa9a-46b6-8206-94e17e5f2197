import math
import os
from io import Bytes<PERSON>
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Union

import av
import numpy as np
import requests
import torch
from PIL import Image
from transformers import PreTrainedTokenizer, ProcessorMixin
from transformers.image_utils import ChannelDimension, get_image_size, load_image, to_numpy_array

from ..extras.constants import IGNORE_INDEX, MediaType
from ..extras.logging import get_logger
from ..extras.packages import is_librosa_available


if is_librosa_available():
    import librosa


if TYPE_CHECKING:
    from av.container import InputContainer
    from av.stream import Stream
    from PIL.Image import Image as ImageObject

    from ..hparams.model_args import ProcessorArguments

    ImageInput = Union[str, bytes, ImageObject]
    VideoInput = Union[str, List[str]]
    AudioInput = str

logger = get_logger(__name__)


class BasePlugin:
    def __init__(
        self, tokenizer: "PreTrainedTokenizer", processor: "ProcessorMixin", processor_args: "ProcessorArguments"
    ) -> None:
        self.processor = processor
        self.tokenizer = tokenizer
        self.processor_args = processor_args

        # configure in args not in env
        self.infer_mode = None

    def _preprocess_image(self, image: "ImageObject", from_video=False, **kwargs) -> "ImageObject":
        r"""
        Pre-processes a single image.
        """
        max_pixels = self.processor_args.video_resolution if from_video else self.processor_args.image_resolution
        min_pixels = self.processor_args.min_resolution
        if max_pixels > 0 and (image.width * image.height) > max_pixels:
            resize_factor = math.sqrt(max_pixels / (image.width * image.height))
            width, height = int(image.width * resize_factor), int(image.height * resize_factor)
            image = image.resize((width, height), resample=Image.NEAREST)

        if min_pixels > 0 and (image.width * image.height) < min_pixels:
            resize_factor = math.sqrt(min_pixels / (image.width * image.height))
            width, height = int(image.width * resize_factor), int(image.height * resize_factor)
            image = image.resize((width, height), resample=Image.NEAREST)

        if image.mode != "RGB":
            image = image.convert("RGB")

        return image

    def _regularize_image(self, image: "ImageInput", **kwargs) -> "ImageObject":
        r"""
        Regularizes image to avoid error. Including reading and pre-processing.
        """
        if isinstance(image, bytes):  # ai-lake data type
            image = Image.open(BytesIO(image))
        elif isinstance(image, dict):
            if image["bytes"] is not None:
                image = Image.open(BytesIO(image["bytes"]))
            else:
                image = Image.open(image["path"])
        else:
            image = load_image(image, timeout=30)
        return self._preprocess_image(image, **kwargs)

    def _regularize_video(self, video: "VideoInput", **kwargs) -> List["ImageObject"]:
        r"""
        Regularizes video to avoid error. Including reading, resizing and converting.
        """
        if not isinstance(video, list):
            frames = self.load_video_as_frames(video)
        else:
            frames = video
        return [self._regularize_image(frame, from_video=True) for frame in frames]

    def _regularize_audio(self, audio: "AudioInput", **kwargs) -> "np.ndarray":
        r"""
        Regularizes audio to avoid error. Including reading and converting.
        """
        if audio.startswith(("http://", "https://")):
            audio = BytesIO(requests.get(audio, timeout=None).content)
        audio, _ = librosa.load(audio, sr=self.sampling_rate)
        return audio

    def _get_video_sample_indices(self, video_stream: "Stream", video_container: "InputContainer"):
        total_frames = video_stream.frames
        # +1 for frame 0, a 9s video should get 10 frames when fps=1
        if video_stream.duration is None or total_frames is None or total_frames == 0:  # this fork cost more
            total_frames = sum([1 for _ in video_container.decode(video_stream)])
            sample_frames = (total_frames / float(video_stream.average_rate)) * self.processor_args.video_fps + 1
        else:
            sample_frames = float(video_stream.duration * video_stream.time_base) * self.processor_args.video_fps + 1
        sample_frames = min(total_frames, self.processor_args.video_maxlen, sample_frames)
        sample_frames = math.floor(sample_frames)
        sample_indices = np.linspace(0, total_frames - 1, sample_frames).astype(np.int32)
        return sample_indices

    def load_video_as_frames(self, video_path: str):
        with av.open(video_path, "r", metadata_errors="ignore") as container:
            video_stream = next(stream for stream in container.streams if stream.type == "video")
            sample_indices = self._get_video_sample_indices(video_stream, container)
            frames = []
            container.seek(0)
            for frame_idx, frame in enumerate(container.decode(video_stream)):
                if frame_idx in sample_indices:
                    frames.append(frame.to_image())
        return frames

    def _flatten(self, array: List[Union[int, List[int]]]):
        flatten_array = []
        for item in array:
            if isinstance(item, list):
                flatten_array.extend(item)
            else:
                flatten_array.append(item)
        return flatten_array

    def _images_to_features(self, images: List["ImageObject"]):
        return self.processor.image_processor(images, return_tensors="pt")

    def _videos_to_features(self, videos: List[List["ImageObject"]]):
        if hasattr(self.processor, "video_processor"):
            return self.processor.video_processor(videos, return_tensors="pt")
        else:
            return self.processor(videos, media_type=MediaType.VIDEO, return_tensors="pt")

    def _audios_to_features(self, audios: List[Any]):
        return self.processor(audios, media_type=MediaType.AUDIO, return_tensors="pt")

    def get_mm_features(
        self,
        images: Optional[List["ImageInput"]] = None,
        videos: Optional[List["VideoInput"]] = None,
        audios: Optional[List["AudioInput"]] = None,
    ):
        mm_features = {}
        if images is not None and len(images) > 0:
            images = [self._regularize_image(im) for im in images]
            mm_features.update(self._images_to_features(images))
        if videos is not None and len(videos) > 0:
            videos = [self._regularize_video(vid) for vid in videos]
            mm_features.update(self._videos_to_features(videos))
        if audios is not None and len(audios) > 0:
            audios = [self._regularize_audio(audio) for audio in audios]
            mm_features.update(self._audios_to_features(audios))
        return mm_features

    def _process_single_mm_input(
        self,
        input_ids: List[int],
        labels: Optional[List[int]] = None,
        attention_mask: Optional[List[int]] = None,
        images: Optional[List["ImageInput"]] = None,
        videos: Optional[List["VideoInput"]] = None,
        audios: Optional[List["AudioInput"]] = None,
    ):
        mm_features = self.get_mm_features(images=images, videos=videos, audios=audios)
        processed_token_ids = self.process_single_token_ids(
            input_ids=input_ids, labels=labels, attention_mask=attention_mask, mm_features=mm_features
        )
        return processed_token_ids, mm_features

    def _get_mock_single_mm_input(
        self,
        input_ids: List[int],
        labels: Optional[List[int]] = None,
        attention_mask: Optional[List[int]] = None,
        images: Optional[List["ImageInput"]] = None,
        videos: Optional[List["VideoInput"]] = None,
        audios: Optional[List["AudioInput"]] = None,
    ):
        input_ids = [self.image_token_id]
        if labels is not None:
            labels = [IGNORE_INDEX]
        if attention_mask is not None:
            attention_mask = [1]
        if audios is not None:
            raise ValueError("not support audios failover")
        images = [Image.new('RGB', (224, 224), (255, 255, 255))]
        return self._process_single_mm_input(
            input_ids=input_ids, labels=labels, attention_mask=attention_mask, images=images
        )

    def append_mock_media(self, feature: Dict[str, Any]):
        feature["image"] = [Image.new('RGB', (224, 224), (255, 255, 255))]
        feature["input_ids"] = feature["input_ids"] + [self.image_token_id]
        if "labels" in feature:
            feature["labels"] = feature["labels"] + [IGNORE_INDEX]
        if "attention_mask" in feature:
            feature["attention_mask"] = feature["attention_mask"] + [1]
        return feature

    def process_single_mm_input(
        self,
        input_ids: List[int],
        labels: Optional[List[int]] = None,
        attention_mask: Optional[List[int]] = None,
        images: Optional[List["ImageInput"]] = None,
        videos: Optional[List["VideoInput"]] = None,
        audios: Optional[List["AudioInput"]] = None,
        failover: bool = True
    ):
        try:
            processed_token_ids, mm_features = self._process_single_mm_input(
                input_ids=input_ids,
                labels=labels,
                attention_mask=attention_mask,
                images=images,
                videos=videos,
                audios=audios,
            )
        except Exception as e:  # TODO: specify load and process error
            mm_input = (images or []) + (videos or []) + (audios or [])
            mm_input = [i if isinstance(i, str) or isinstance(i, list) else "BYTES_DATA" for i in mm_input]
            logger.error(f"Failed to process mm input: {e}, mm_input: {mm_input}, input_ids: {input_ids}")
            if not failover:
                return None, None
            return self._get_mock_single_mm_input(input_ids, labels, attention_mask, images, videos, audios)
        return processed_token_ids, mm_features

    def process_single_token_ids(
        self,
        input_ids: List[int],
        labels: Optional[List[int]] = None,
        attention_mask: Optional[List[int]] = None,
        mm_features: Dict[str, Any] = None,
    ):
        processed_token_ids = {"input_ids": input_ids}
        if labels:
            processed_token_ids["labels"] = labels
        if attention_mask:
            processed_token_ids["attention_mask"] = attention_mask
        return processed_token_ids

    def mm_collate_fn(
        self,
        mm_features: List[Dict[str, Any]],
        collated_text_features: Optional[Dict[str, Any]] = None,
        batch_input_ids: Optional[List[List[int]]] = None,
    ):
        collated_features = {}
        for mm_feature in mm_features:
            for k, v in mm_feature.items():
                if k not in collated_features:
                    collated_features[k] = []
                collated_features[k].append(v)
        collated_features = {k: torch.concat(v) for k, v in collated_features.items()}
        return collated_features


class CommonPlugin(BasePlugin):
    pass


class Qwen2VLPlugin(CommonPlugin):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.image_token_id = self.tokenizer.convert_tokens_to_ids('<|image_pad|>')
        self.video_token_id = self.tokenizer.convert_tokens_to_ids('<|video_pad|>')
        self.image_placeholder_token_id = self.tokenizer.convert_tokens_to_ids('<|image_pad|>')
        self.video_placeholder_token_id = self.tokenizer.convert_tokens_to_ids('<|video_pad|>')
        self.vision_start_token_id = self.tokenizer.convert_tokens_to_ids('<|vision_start|>')
        self.vision_end_token_id = self.tokenizer.convert_tokens_to_ids('<|vision_end|>')
        self.merge_length = self.processor.image_processor.merge_size ** 2

    def _process_single_token_ids(
        self,
        input_ids: List[int],
        media_type: MediaType,
        grid_thw: torch.Tensor,
        labels: List[int] = None,
        **kwargs
    ):
        placeholder_token_id = (
            self.image_placeholder_token_id if media_type == MediaType.IMAGE else self.video_placeholder_token_id
        )
        image_index = 0
        media_token_id = self.image_token_id if media_type == MediaType.IMAGE else self.video_token_id
        for idx, token_id in enumerate(input_ids):
            if token_id == media_token_id:
                placeholder_num = grid_thw[image_index].prod() // self.merge_length
                placeholder_token_ids = [placeholder_token_id] * placeholder_num
                placeholder_sequence = [self.vision_start_token_id] + placeholder_token_ids + [self.vision_end_token_id]
                input_ids[idx] = placeholder_sequence
                if labels:
                    labels[idx] = [IGNORE_INDEX] * len(placeholder_sequence)
                image_index += 1

        return self._flatten(input_ids), self._flatten(labels) if labels is not None else labels

    def process_single_token_ids(
        self,
        input_ids: List[int],
        labels: Optional[List[int]] = None,
        attention_mask: Optional[List[int]] = None,
        mm_features: Dict[str, Any] = None,
    ):
        if "image_grid_thw" in mm_features:
            input_ids, labels = self._process_single_token_ids(
                input_ids=input_ids,
                labels=labels,
                media_type=MediaType.IMAGE,
                grid_thw=mm_features.get("image_grid_thw"),
            )
        if "video_grid_thw" in mm_features:
            input_ids, labels = self._process_single_token_ids(
                input_ids=input_ids,
                labels=labels,
                media_type=MediaType.VIDEO,
                grid_thw=mm_features.get("video_grid_thw"),
            )
        attention_mask = [1] * len(input_ids)
        return super().process_single_token_ids(
            input_ids=input_ids, labels=labels, attention_mask=attention_mask, mm_features=mm_features
        )

    def _videos_to_features(self, videos):
        return self.processor.image_processor(None, videos=videos, return_tensors="pt")

    def mm_collate_fn(
        self,
        mm_features: List[Dict[str, Any]],
        collated_text_features: Optional[Dict[str, Any]] = None,
        batch_input_ids: Optional[List[List[int]]] = None,
    ):
        mm_features = super().mm_collate_fn(mm_features, collated_text_features, batch_input_ids)
        image_processor = self.processor.image_processor
        if (
            "second_per_grid_ts" in getattr(image_processor, "model_input_names", [])
            and "video_grid_thw" in mm_features
        ):
            # needed for qwen2.5 vl
            mm_features["second_per_grid_ts"] = [
                image_processor.temporal_patch_size / self.processor_args.video_fps
            ] * len(mm_features["video_grid_thw"])
        return mm_features


class Qwen2AudioPlugin(CommonPlugin):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.audio_token_id = self.tokenizer.convert_tokens_to_ids('<|AUDIO|>')
        self.audio_bos_token_id = self.tokenizer.convert_tokens_to_ids('<|audio_bos|>')
        self.audio_eos_token_id = self.tokenizer.convert_tokens_to_ids('<|audio_eos|>')
        self.sampling_rate = 16000

    def process_single_token_ids(
        self,
        input_ids: List[int],
        labels: Optional[List[int]] = None,
        attention_mask: Optional[List[int]] = None,
        mm_features: Dict[str, Any] = None,
    ):
        audio_lengths = mm_features["feature_attention_mask"].sum(-1).tolist()
        audio_index = 0
        for i in range(len(input_ids)):
            if input_ids[i] == self.audio_token_id:
                audio_length = audio_lengths.pop(0)
                input_length = (audio_length - 1) // 2 + 1
                num_audio_tokens = (input_length - 2) // 2 + 1
                placeholder_sequence = [self.audio_token_id] * num_audio_tokens
                if i == 0 or input_ids[i - 1] != self.audio_bos_token_id:
                    placeholder_sequence = [self.audio_bos_token_id] + placeholder_sequence + [self.audio_eos_token_id]
                input_ids[i] = placeholder_sequence
                if labels:
                    labels[i] = [IGNORE_INDEX] * len(placeholder_sequence)
                audio_index += 1
        input_ids = self._flatten(input_ids)
        labels = self._flatten(labels) if labels is not None else labels
        attention_mask = [1] * len(input_ids) if attention_mask is not None else attention_mask
        return super().process_single_token_ids(input_ids, labels, attention_mask, mm_features)


class InternVLPlugin(CommonPlugin):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.image_token_id = self.tokenizer.convert_tokens_to_ids('<IMG_CONTEXT>')
        self.vision_start_token_id = self.tokenizer.convert_tokens_to_ids('<img>')
        self.vision_end_token_id = self.tokenizer.convert_tokens_to_ids('</img>')
        self.num_image_token = self.processor.num_image_token
        self.num_patches_list = None

    def _get_video_tokens(self, num_patches: List[int]):
        placeholder_sequence = []
        for i in range(len(num_patches)):
            num_frame_token_ids = self.tokenizer.encode(f"Frame{i+1}: ", add_special_tokens=False)
            placeholder_num = num_patches[i] * self.num_image_token
            placeholder_token_ids = [self.image_token_id] * placeholder_num
            placeholder_sequence += (
                num_frame_token_ids + [self.vision_start_token_id] + placeholder_token_ids + [self.vision_end_token_id]
            )
        return placeholder_sequence

    def _get_image_tokens(self, num_patch: int):
        placeholder_num = num_patch * self.num_image_token
        return [self.vision_start_token_id] + [self.image_token_id] * placeholder_num + [self.vision_end_token_id]

    def process_single_token_ids(
        self,
        input_ids: List[int],
        labels: Optional[List[int]] = None,
        attention_mask: Optional[List[int]] = None,
        mm_features: Dict[str, Any] = None,
    ):
        num_patches_list = mm_features.pop("num_patches_list", torch.Tensor([]))
        if labels:
            mm_features.update({"image_flags": torch.ones(num_patches_list.sum())})
        media_index = 0
        for i in range(len(input_ids)):
            if input_ids[i] == self.image_token_id:
                if num_patches_list.dim() == 2:
                    placeholder_sequence = self._get_video_tokens(num_patches_list[media_index].tolist())
                else:
                    placeholder_sequence = self._get_image_tokens(num_patches_list[media_index].item())
                input_ids[i] = placeholder_sequence
                if labels:
                    labels[i] = [IGNORE_INDEX] * len(placeholder_sequence)
                media_index += 1
        assert media_index == num_patches_list.shape[0]
        input_ids = self._flatten(input_ids)
        labels = self._flatten(labels) if labels is not None else labels
        attention_mask = [1] * len(input_ids) if attention_mask is not None else attention_mask
        return super().process_single_token_ids(input_ids, labels, attention_mask, mm_features)

    def mm_collate_fn(
        self,
        mm_features: List[Dict[str, Any]],
        collated_text_features: Optional[Dict[str, Any]] = None,
        batch_input_ids: Optional[List[List[int]]] = None,
    ):
        image_flags_sum = 0
        image_flag = "image_flags" in mm_features[0]
        for mm_feature in mm_features:
            image_flags_sum += mm_feature.pop("image_flags", torch.Tensor([])).sum().int().item()
            mm_feature.pop("num_patches_list", None)
        collated_mm_features = super().mm_collate_fn(mm_features, collated_text_features, batch_input_ids)
        if len(mm_features) > 0 and image_flag:
            collated_mm_features["image_flags"] = torch.ones((image_flags_sum))
        return collated_mm_features


class MllamaPlugin(CommonPlugin):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.image_token_id = self.tokenizer.convert_tokens_to_ids('<|image|>')
        self.pad_to_multiple_of = 8 if self.tokenizer.padding_side == "right" else 1

    def _process_single_mm_input(
        self,
        input_ids: List[int],
        labels: Optional[List[int]] = None,
        attention_mask: Optional[List[int]] = None,
        images: Optional[List["ImageInput"]] = None,
        videos: Optional[List["VideoInput"]] = None,
        audios: Optional[List["AudioInput"]] = None,
    ):
        from transformers.models.mllama.processing_mllama import get_cross_attention_token_mask

        mm_features = self.get_mm_features(images=images, videos=videos, audios=audios)
        cross_attention_token_mask = get_cross_attention_token_mask(input_ids, self.image_token_id)
        mm_features["cross_attention_token_mask"] = cross_attention_token_mask
        processed_token_ids = self.process_single_token_ids(
            input_ids=input_ids, labels=labels, attention_mask=attention_mask, mm_features=mm_features
        )
        return processed_token_ids, mm_features

    def mm_collate_fn(
        self,
        mm_features: List[Dict[str, Any]],
        collated_text_features: Optional[Dict[str, Any]] = None,
        batch_input_ids: Optional[List[List[int]]] = None,
    ):
        from transformers.models.mllama.processing_mllama import convert_sparse_cross_attention_mask_to_dense

        if batch_input_ids is not None:
            if len(batch_input_ids) == 0:
                return {}
            seq_len = len(batch_input_ids[0])
            assert len(batch_input_ids) <= 1, "Only support batch size 1 for mllama in inference"
        else:
            seq_len = collated_text_features["input_ids"].size(1)
        cross_attention_token_mask = [mm_feature.pop("cross_attention_token_mask", []) for mm_feature in mm_features]
        num_tiles = [mm_feature.pop("num_tiles", []) for mm_feature in mm_features]
        mm_features = super().mm_collate_fn(mm_features, collated_text_features, batch_input_ids)
        if len(mm_features) == 0:
            return {}
        num_tiles = self._flatten(num_tiles)
        cross_attention_mask = convert_sparse_cross_attention_mask_to_dense(
            cross_attention_token_mask,
            length=seq_len,
            num_tiles=num_tiles,
            max_num_tiles=self.processor.image_processor.max_image_tiles,
        )
        mm_features["cross_attention_mask"] = torch.from_numpy(cross_attention_mask)
        if batch_input_ids is not None and self.infer_mode and "vllm" in self.infer_mode:
            mm_features["num_tiles"] = num_tiles[0]
        return mm_features


class LLavaPlugin(CommonPlugin):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.image_token_id = self.tokenizer.convert_tokens_to_ids("<image>")
        self.patch_size = getattr(self.processor, "patch_size")
        self.num_additional_image_tokens = getattr(self.processor, "num_additional_image_tokens")
        self.vision_feature_select_strategy = getattr(self.processor, "vision_feature_select_strategy")

        if self.patch_size is None:
            self.patch_size = 14
        if self.num_additional_image_tokens is None:
            self.num_additional_image_tokens = 1
        if self.vision_feature_select_strategy is None:
            self.vision_feature_select_strategy = "default"

    def _get_image_tokens(self, pixel_values: "torch.Tensor"):
        height, width = get_image_size(to_numpy_array(pixel_values[0]))
        num_image_tokens = (height // self.patch_size) * (width // self.patch_size) + self.num_additional_image_tokens
        if self.vision_feature_select_strategy == "default":
            num_image_tokens -= 1
        return [self.image_token_id] * num_image_tokens

    def process_single_token_ids(
        self,
        input_ids: List[int],
        labels: Optional[List[int]] = None,
        attention_mask: Optional[List[int]] = None,
        mm_features: Dict[str, Any] = None,
    ):
        image_index = 0
        for i in range(len(input_ids)):
            placeholder_sequence = None
            if input_ids[i] == self.image_token_id:
                placeholder_sequence = self._get_image_tokens(mm_features["pixel_values"])
                input_ids[i] = placeholder_sequence
                image_index += 1
            if labels and placeholder_sequence:
                labels[i] = [IGNORE_INDEX] * len(placeholder_sequence)
        input_ids, labels = self._flatten(input_ids), self._flatten(labels) if labels is not None else None
        attention_mask = [1] * len(input_ids) if attention_mask is not None else None
        return super().process_single_token_ids(input_ids, labels, attention_mask, mm_features)


class LlavaOneVisionPlugin(CommonPlugin):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.image_token_id = self.tokenizer.convert_tokens_to_ids('<image>')
        self.video_token_id = self.tokenizer.convert_tokens_to_ids('<video>')

        num_image_tokens = getattr(self.processor, 'num_image_tokens', 1)
        patches_height_width = int(math.sqrt(num_image_tokens))
        self.pooled_height_width = math.ceil(patches_height_width / 2)
        self.num_additional_image_tokens = 1
        self.channel_dim = ChannelDimension.FIRST

    def _get_video_tokens(self, pixel_values_videos: "torch.Tensor"):
        num_frames = pixel_values_videos.shape[1]  # frame dim is always after batch dim
        num_video_tokens = (num_frames * self.pooled_height_width * self.pooled_height_width) + 1
        return [self.video_token_id] * num_video_tokens

    def _get_image_tokens(self, pixel_values: "torch.Tensor", image_size: "torch.Tensor"):
        if not isinstance(image_size, (list, tuple)):
            # cast to list to avoid numerical precision errors when calculating unpadding
            image_size = image_size.tolist()
        orig_height, orig_width = image_size
        height, width = get_image_size(to_numpy_array(pixel_values[0][0]), channel_dim=self.channel_dim)
        num_image_tokens = self.processor._get_number_of_features(orig_height, orig_width, height, width)
        if self.processor.vision_feature_select_strategy == "default":
            num_image_tokens -= self.num_additional_image_tokens
        return [self.image_token_id] * num_image_tokens

    def process_single_token_ids(
        self,
        input_ids: List[int],
        labels: Optional[List[int]] = None,
        attention_mask: Optional[List[int]] = None,
        mm_features: Dict[str, Any] = None,
    ):
        image_index, video_index = 0, 0
        for i in range(len(input_ids)):
            placeholder_sequence = None
            if input_ids[i] == self.image_token_id:
                placeholder_sequence = self._get_image_tokens(
                    mm_features["pixel_values"], image_size=mm_features["image_sizes"][image_index]
                )
                input_ids[i] = placeholder_sequence
                image_index += 1
            elif input_ids[i] == self.video_token_id:
                placeholder_sequence = self._get_video_tokens(mm_features["pixel_values_videos"])
                input_ids[i] = placeholder_sequence
                video_index += 1
            if labels and placeholder_sequence:
                labels[i] = [IGNORE_INDEX] * len(placeholder_sequence)
        input_ids, labels = self._flatten(input_ids), self._flatten(labels) if labels is not None else None
        attention_mask = [1] * len(input_ids) if attention_mask is not None else None
        if "image_sizes" in mm_features:
            assert len(mm_features["image_sizes"]) == image_index
        if "pixel_values_videos" in mm_features:
            assert len(mm_features["pixel_values_videos"]) == video_index
        return super().process_single_token_ids(input_ids, labels, attention_mask, mm_features)


class LlavaNextVideoPlugin(LlavaOneVisionPlugin):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.channel_dim = None
        self.num_additional_image_tokens = self.processor.num_additional_image_tokens
        self.patch_size = self.processor.patch_size

    def _get_video_tokens(self, pixel_values_videos: "torch.Tensor"):
        height, width = get_image_size(pixel_values_videos[0][0])
        num_frames = pixel_values_videos.shape[1]  # frame dim is always after batch dim
        num_image_tokens = (height // self.patch_size) * (width // self.patch_size)
        num_video_tokens = num_image_tokens // 4 * num_frames  # divide by 4 needed for avg pooling layer
        return [self.video_token_id] * num_video_tokens


class TBStars008VLPlugin(CommonPlugin):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.image_token_id = self.tokenizer.convert_tokens_to_ids("<image>")
        self.video_token_id = self.tokenizer.convert_tokens_to_ids("<video>")
        self.vision_start_id = self.tokenizer.convert_tokens_to_ids("<image>")
        self.vision_end_id = self.tokenizer.convert_tokens_to_ids("</image>")
        self.end_sep_id = 213 # represent \n
        self.concat_sep_id = 127996 # represent \n\n
        self.image_place_holder_id = self.tokenizer.convert_tokens_to_ids("<PLACEHOLDER_33>")
        self.video_place_holder_id = self.tokenizer.convert_tokens_to_ids("<PLACEHOLDER_34>")

    def process_single_token_ids(
        self,
        input_ids: List[int],
        labels: Optional[List[int]] = None,
        attention_mask: Optional[List[int]] = None,
        mm_features: Dict[str, Any] = None,
    ):
        def _get_image_token_ids(media_len):
            return [self.vision_start_id] + [self.image_place_holder_id] * media_len + [self.vision_end_id]

        def _get_video_token_ids(media_lens):
            res = []
            for media_len in media_lens:
                if media_len <= 0:
                    continue
                res += [self.vision_start_id] + [self.video_place_holder_id] * media_len + [self.vision_end_id, self.end_sep_id]
            return res

        image_index, video_index = 0, 0
        image_lens, video_lens = [], []
        proj_len = getattr(self.processor.image_processor, "perceiver_num_queries", 144)
        if "crop_positions" in mm_features:
            crop_positions = mm_features["crop_positions"]
            cumsum_positions = torch.tensor(
                [torch.allclose(p, crop_positions[0]) for p in crop_positions] + [True]
            ).nonzero()
            image_lens = ((cumsum_positions[1:] - cumsum_positions[:-1]) * proj_len).squeeze(1).tolist()
        if "video_chunk_sizes" in mm_features:
            video_lens = (mm_features["video_chunk_sizes"] * proj_len).int().tolist()

        for i in range(len(input_ids)):
            if input_ids[i] == self.image_token_id:
                input_ids[i] = _get_image_token_ids(image_lens[image_index])
                image_index += 1
            elif input_ids[i] == self.video_token_id:
                input_ids[i] = _get_video_token_ids(video_lens[video_index])
                video_index += 1
            else:
                continue
            if labels is not None:
                labels[i] = [IGNORE_INDEX] * len(input_ids[i])
        assert (
            image_index == len(image_lens) and video_index == len(video_lens)
        ), f"image_index: {image_index}, video_index: {video_index}, image_lens: {image_lens}, video_lens: {video_lens}"
        input_ids = self._flatten(input_ids)
        if labels is not None:
            labels = self._flatten(labels)
        if attention_mask is not None:
            attention_mask = [1] * len(input_ids)
        return super().process_single_token_ids(input_ids, labels, attention_mask, mm_features)

    def _videos_to_features(self, videos):
        return self.processor.image_processor(None, videos=videos, return_tensors="pt")

    def mm_collate_fn(
        self,
        mm_features: List[Dict[str, Any]],
        collated_text_features: Optional[Dict[str, Any]] = None,
        batch_input_ids: Optional[List[List[int]]] = None,
    ):
        video_chunk_sizes = None
        for f in mm_features:
            video_chunk_sizes = f.pop("video_chunk_sizes", None)  # not needed in forward
        mm_collated_features = super().mm_collate_fn(mm_features, collated_text_features, batch_input_ids)
        if video_chunk_sizes is not None: # mock value for vllm infer
            mm_collated_features["video_chunk_sizes"] = video_chunk_sizes.unsqueeze(0)
        return mm_collated_features


class DeepseekVL2Plugin(CommonPlugin):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.image_token_id = self.tokenizer.convert_tokens_to_ids("<image>")

    def process_single_token_ids(
        self,
        input_ids: List[int],
        labels: Optional[List[int]] = None,
        attention_mask: Optional[List[int]] = None,
        mm_features: Dict[str, Any] = None,
    ):
        image_index = 0
        num_image_tokens = mm_features.pop("num_image_tokens", None)
        for i in range(len(input_ids)):
            if input_ids[i] == self.image_token_id:
                input_ids[i] = [self.image_token_id] * num_image_tokens[image_index]
                if labels:
                    labels[i] = [IGNORE_INDEX] * len(input_ids[i])
                image_index += 1
        input_ids, labels = self._flatten(input_ids), self._flatten(labels) if labels is not None else None
        attention_mask = [1] * len(input_ids) if attention_mask is not None else None
        assert image_index == len(num_image_tokens), f"image_index: {image_index}, num_image_tokens: {num_image_tokens}"
        return super().process_single_token_ids(input_ids, labels, attention_mask, mm_features)

    def mm_collate_fn(
        self,
        mm_features: List[Dict[str, Any]],
        collated_text_features: Optional[Dict[str, Any]] = None,
        batch_input_ids: Optional[List[List[int]]] = None,
    ):
        """padding images to max_patch_num"""
        max_n_patches = max(feature["images"].shape[0] for feature in mm_features)
        batched_images = []
        for feature in mm_features:
            images = feature["images"]
            n_pads = max_n_patches - images.shape[0]
            if n_pads > 0:
                pad_images = torch.zeros((n_pads, *images.shape[1:]), dtype=images.dtype)
                images = torch.cat([images, pad_images], dim=0)
            batched_images.append(images)
        batched_images = torch.stack(batched_images, dim=0)

        """padding images_spatial_crop to max_n_images"""
        max_n_images = max(feature["images_spatial_crop"].shape[0] for feature in mm_features)
        batched_images_spatial_crop = []
        for feature in mm_features:
            images_spatial_crop = feature["images_spatial_crop"]
            n_pads = max_n_images - feature["images_spatial_crop"].shape[0]
            if n_pads > 0:
                pad_images_spatial_crop = torch.full((n_pads, 2), 0, dtype=images_spatial_crop.dtype)
                images_spatial_crop = torch.cat([images_spatial_crop, pad_images_spatial_crop], dim=0)
            batched_images_spatial_crop.append(images_spatial_crop)
        batched_images_spatial_crop = torch.stack(batched_images_spatial_crop, dim=0)
        return {"images": batched_images, "images_spatial_crop": batched_images_spatial_crop}


class MiniCPMOPlugin(CommonPlugin):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.image_token_id = self.tokenizer.convert_tokens_to_ids("<|image_pad|>")
        self.video_token_id = self.tokenizer.convert_tokens_to_ids("<|video_pad|>")
        self.audio_token_id = self.tokenizer.convert_tokens_to_ids("<|audio|>")
        self.sampling_rate = 16000

    def _videos_to_features(self, videos: List[List["ImageObject"]]):
        return self.processor.image_processor(videos, media_type=MediaType.VIDEO, return_tensors="pt")

    def _audios_to_features(self, audios: List[Any]):
        audio_features, audio_feature_lens, audio_phs = self.processor.audio_feature_extract(
            audios, sampling_rate=self.sampling_rate
        )
        return {
            "audio_features": audio_features,
            "audio_feature_lens": audio_feature_lens,
            "audio_phs": audio_phs,
        }

    def process_single_token_ids(
        self,
        input_ids: List[int],
        labels: Optional[List[int]] = None,
        attention_mask: Optional[List[int]] = None,
        mm_features: Dict[str, Any] = None,
    ):
        image_index, audio_idx = 0, 0
        image_sizes = mm_features.pop("image_sizes", None)
        audio_phs = mm_features.pop("audio_phs", None)
        for i in range(len(input_ids)):
            if input_ids[i] == self.image_token_id or input_ids[i] == self.video_token_id:
                image_placeholder = self.processor.image_processor.get_slice_image_placeholder(
                    image_sizes[0][image_index], image_index, max_slice_nums=None, use_image_id=True
                )
                placeholder_sequence = self.tokenizer.encode(image_placeholder)
                input_ids[i] = placeholder_sequence
                if labels:
                    labels[i] = [IGNORE_INDEX] * len(input_ids[i])
                image_index += 1
            elif input_ids[i] == self.audio_token_id:
                audio_placeholder = audio_phs[0][audio_idx]
                placeholder_sequence = self.tokenizer.encode(audio_placeholder)
                input_ids[i] = placeholder_sequence
                if labels:
                    labels[i] = [IGNORE_INDEX] * len(input_ids[i])
        input_ids, labels = self._flatten(input_ids), self._flatten(labels) if labels is not None else None
        attention_mask = [1] * len(input_ids) if attention_mask is not None else None
        return super().process_single_token_ids(input_ids, labels, attention_mask, mm_features)

    def mm_collate_fn(self, mm_features, collated_text_features = None, batch_input_ids = None):
        if batch_input_ids is not None:
            if len(batch_input_ids) == 0:
                return {}
            assert len(batch_input_ids) <= 1, "Only support batch size 1 for MiniCPMO in inference"
        else:
            batch_input_ids = collated_text_features["input_ids"]

        image_bounds_list, audio_bounds_list, spk_bounds_list = [], [], []
        for input_ids in batch_input_ids:
            if isinstance(input_ids, list):
                input_ids = torch.tensor(input_ids)

            ## image bound
            start_cond = (input_ids == self.tokenizer.im_start_id) | (input_ids == self.tokenizer.slice_start_id)
            end_cond = (input_ids == self.tokenizer.im_end_id) | (input_ids == self.tokenizer.slice_end_id)

            image_start_idx = torch.where(start_cond)[0]
            image_start_idx += 1
            image_end_idx = torch.where(end_cond)[0]

            valid_image_nums = max(len(image_start_idx), len(image_end_idx))

            image_bounds = torch.hstack(
                [
                    image_start_idx[:valid_image_nums].unsqueeze(-1),
                    image_end_idx[:valid_image_nums].unsqueeze(-1),
                ]
            )

            ##  audio bound
            audio_start_idx = torch.where(input_ids == self.tokenizer.audio_start_id)[0]
            audio_end_idx = torch.where(input_ids == self.tokenizer.audio_end_id)[0]
            assert len(audio_start_idx) == len(audio_end_idx)
            audio_bounds = torch.hstack([(audio_start_idx + 1).unsqueeze(-1), audio_end_idx.unsqueeze(-1)])

            spk_start_idx = torch.where(input_ids == self.tokenizer.spk_start_id)[0]
            spk_end_idx = torch.where(input_ids == self.tokenizer.spk_end_id)[0]
            assert len(spk_start_idx) == len(spk_end_idx)
            spk_bounds = torch.hstack([(spk_start_idx + 1).unsqueeze(-1), spk_end_idx.unsqueeze(-1)])

            image_bounds_list.append(image_bounds)
            audio_bounds_list.append(audio_bounds)
            spk_bounds_list.append(spk_bounds)

        batch_mm_features = {
            **mm_features[0],
            "image_bound": torch.stack(image_bounds_list),
            "audio_bounds": torch.stack(audio_bounds_list),
            "spk_bounds": torch.stack(spk_bounds_list),
            "tokenizer": self.tokenizer
        }

        if 'audio_features' not in batch_mm_features:
            batch_mm_features['audio_features'] = []

        if 'pixel_values' not in batch_mm_features:
            batch_mm_features['pixel_values'] = [[]]

        if collated_text_features is None:
            return batch_mm_features

        bsz, seq_length = collated_text_features["input_ids"].shape
        collated_text_features["position_ids"] = torch.arange(seq_length).long().repeat(bsz, 1)

        batch_features = {
            "input_ids": collated_text_features["input_ids"],
            "labels": collated_text_features["labels"],
            "data": batch_mm_features
        }
        batch_features["data"].update(collated_text_features)
        collated_text_features.clear()
        return batch_features


class Gemma3Plugin(CommonPlugin):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.boi_token = "<start_of_image>"
        self.boi_token_id = self.tokenizer.convert_tokens_to_ids(self.boi_token)
        self.image_token_id = self.processor.image_token_id
        self.full_image_sequence = self.tokenizer.encode(self.processor.full_image_sequence, add_special_tokens=False)

    def process_single_token_ids(
        self,
        input_ids: List[int],
        labels: Optional[List[int]] = None,
        attention_mask: Optional[List[int]] = None,
        mm_features: Dict[str, Any] = None,
    ):
        image_index = 0
        batch_num_crops = mm_features.pop("num_crops", [[]])
        num_crops = batch_num_crops[0]
        # apply crops
        if sum(num_crops) > 0:
            for i in range(len(input_ids)):
                placeholder_sequence = None
                if input_ids[i] == self.boi_token_id:
                    if num_crops[image_index]:
                        formatted_image_text = (
                            f"Here is the original image {self.boi_token} and here are some crops to help you see better "
                            + " ".join([self.boi_token] * num_crops[image_index])
                        )
                        placeholder_sequence = self.tokenizer.encode(formatted_image_text, add_special_tokens=False)
                        input_ids[i] = placeholder_sequence
                        if labels:
                            labels[i] = [IGNORE_INDEX] * len(placeholder_sequence)
                        image_index += 1
            input_ids, labels = self._flatten(input_ids), self._flatten(labels) if labels is not None else None

        # process image
        image_index = 0
        for i in range(len(input_ids)):
            placeholder_sequence = None
            if input_ids[i] == self.boi_token_id:
                placeholder_sequence = self.full_image_sequence
                input_ids[i] = placeholder_sequence
                if labels:
                    labels[i] = [IGNORE_INDEX] * len(placeholder_sequence)
                image_index += 1
        input_ids, labels = self._flatten(input_ids), self._flatten(labels) if labels is not None else None
        attention_mask = [1] * len(input_ids) if attention_mask is not None else None

        # Add token type ids manually, as tokenizer can't do arbitrary position token types
        input_tensor = torch.tensor(input_ids)
        mm_token_type_ids = (input_tensor == self.image_token_id).to(torch.int64)
        mm_features["token_type_ids"] = mm_token_type_ids

        return super().process_single_token_ids(input_ids, labels, attention_mask, mm_features)

    def mm_collate_fn(
        self,
        mm_features: List[Dict[str, Any]],
        collated_text_features: Optional[Dict[str, Any]] = None,
        batch_input_ids: Optional[List[List[int]]] = None,
    ):
        token_type_ids = [mm_feature.pop("token_type_ids") for mm_feature in mm_features]
        max_input_length = max(token_type_id.shape[0] for token_type_id in token_type_ids)
        new_token_type_ids = []
        mm_features = super().mm_collate_fn(mm_features, collated_text_features, batch_input_ids)

        # generation, left padding
        if batch_input_ids is not None:
            for token_type_id in token_type_ids:
                n_pads = max_input_length - token_type_id.shape[0]
                padding_sequence = torch.zeros(n_pads, dtype=token_type_id.dtype)
                token_type_id = torch.cat([padding_sequence, token_type_id], dim=0)
                new_token_type_ids.append(token_type_id)

        # training, right padding
        if collated_text_features is not None:
            for token_type_id in token_type_ids:
                n_pads = max_input_length - token_type_id.shape[0]
                padding_sequence = torch.zeros(n_pads, dtype=token_type_id.dtype)
                token_type_id = torch.cat([token_type_id, padding_sequence], dim=0)
                new_token_type_ids.append(token_type_id)

        if len(mm_features) > 0:
            mm_features["token_type_ids"] = torch.stack(new_token_type_ids)

        return mm_features

PLUGINS = {
    "base": BasePlugin,
    "glm4v": CommonPlugin,
    "chatml": CommonPlugin,
    "turing": CommonPlugin,
    "qwen2-vl": Qwen2VLPlugin,
    "qvq_preview": Qwen2VLPlugin,
    "qwen2-audio": Qwen2AudioPlugin,
    "internvl2": InternVLPlugin,
    "internvl1": InternVLPlugin,
    "internvl2_Phi_3": InternVLPlugin,
    "llama3_v": MllamaPlugin,
    "vicuna": LLavaPlugin,
    "vip-llava-hf": LLavaPlugin,
    "mistral": LlavaOneVisionPlugin,
    "vicuna-next": LlavaOneVisionPlugin,
    "llava-next-yi": LlavaOneVisionPlugin,
    "llava_onevision_qwen2": LlavaOneVisionPlugin,
    "llava_next_video": LlavaNextVideoPlugin,
    "tbstars008vl": TBStars008VLPlugin,
    "deepseek-vl2": DeepseekVL2Plugin,
    "minicpmo": MiniCPMOPlugin,
    "minicpmo-tts": MiniCPMOPlugin,
    "gemma3": Gemma3Plugin,
}

def get_mm_plugin(
    name: str,
    tokenizer: "PreTrainedTokenizer",
    processor: "ProcessorMixin",
    processor_args: Optional["ProcessorArguments"],
) -> "BasePlugin":
    plugin_class = PLUGINS.get(name, None)
    if plugin_class is None:
        plugin_class = BasePlugin
    return plugin_class(tokenizer=tokenizer, processor=processor, processor_args=processor_args)
