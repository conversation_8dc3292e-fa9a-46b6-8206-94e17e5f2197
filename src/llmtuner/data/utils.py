import functools
from enum import Enum, unique
from itertools import chain
from typing import TYPE_CHECKING, Any, Dict, List, Sequence, Set, Union
import gc

import torch
from datasets import DatasetDict
from PIL import Image

from ..extras.constants import MediaType
from ..extras.logging import get_logger


if TYPE_CHECKING:
    from datasets import Dataset, IterableDataset

    from llmtuner.hparams import DataArguments


logger = get_logger(__name__)


SLOTS = Sequence[Union[str, Set[str], Dict[str, str]]]


@unique
class Role(str, Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    FUNCTION = "function"
    OBSERVATION = "observation"

    def __str__(self):
        return self.value


def split_dataset(
    dataset: Union["Dataset", "IterableDataset"], data_args: "DataArguments", seed: int
) -> "DatasetDict":
    r"""
    Splits the dataset and returns a dataset dict containing train set and validation set.

    Supports both map dataset and iterable dataset.
    """
    if data_args.streaming:
        dataset = dataset.shuffle(buffer_size=data_args.buffer_size, seed=seed)
        val_set = dataset.take(int(data_args.val_size))
        train_set = dataset.skip(int(data_args.val_size))
        return DatasetDict({"train": train_set, "validation": val_set})
    else:
        val_size = int(data_args.val_size) if data_args.val_size > 1 else data_args.val_size
        dataset = dataset.train_test_split(test_size=val_size, seed=seed)
        return DatasetDict({"train": dataset["train"], "validation": dataset["test"]})

def post_process_labels_for_visual_model(
    features: Sequence[Dict[str, Any]],
    template,
    image_features: Dict[str, List[Union[torch.Tensor, int]]],
    stage: str = "sft",
    media_type: MediaType = MediaType.IMAGE,
):
    if not image_features:
        return features
    new_features = []
    if stage != 'rm' and stage != 'kto':
        input_ids = []
        labels = []
        attention_masks = []
        for feature in features:
            input_ids.append(feature["input_ids"])
            if "labels" in feature:
                labels.append(feature["labels"])
            if "attention_mask" in feature:
                attention_masks.append(feature["attention_mask"])
        template.post_image_token_process(
            input_ids,
            labels=labels,
            attention_mask=attention_masks,
            image_features=image_features,
            media_type=media_type,
            is_trainable=True
        )
        for input_id, label, attention_mask in zip(input_ids, labels, attention_masks):
            new_features.append({"input_ids": input_id, "labels": label, "attention_mask": attention_mask})
    elif stage == 'kto':
        input_ids = []
        labels = []
        attention_masks = []
        kl_input_ids = []
        kl_labels = []
        kl_attention_masks = []
        for feature in features:
            input_ids.append(feature["input_ids"])
            labels.append(feature["labels"])
            attention_masks.append(feature["attention_mask"])
            kl_input_ids.append(feature["kl_input_ids"])
            kl_labels.append(feature["kl_labels"])
            kl_attention_masks.append(feature["kl_attention_mask"])
            kto_tags = feature["kto_tags"]
        template.post_image_token_process(input_ids, labels=labels, attention_mask=attention_masks,
                                          image_features=image_features, media_type=media_type, is_trainable=True)
        template.post_image_token_process(kl_input_ids, kl_labels, kl_attention_masks, 
                                          image_features=image_features, media_type=media_type, is_trainable=True)
        for input_id, label, attention_mask, \
            kl_input_id, kl_label, kl_attention_mask in \
            zip(input_ids, labels, attention_masks, \
                kl_input_ids, kl_labels, kl_attention_masks):
            new_features.append({
                "input_ids": input_id,
                "labels": label,
                "attention_mask": attention_mask,
                "kl_input_ids": kl_input_id,
                "kl_labels": kl_label,
                "kl_attention_mask": kl_attention_mask,
                "kto_tags": kto_tags
            })
    elif stage == 'rm':
        chosen_input_ids = []
        chosen_labels = []
        chosen_attention_masks = []
        rejected_input_ids = []
        rejected_labels = []
        rejected_attention_masks = []
        for feature in features:
            chosen_input_ids.append(feature["chosen_input_ids"])
            chosen_labels.append(feature["chosen_labels"])
            chosen_attention_masks.append(feature["chosen_attention_mask"])
            rejected_input_ids.append(feature["rejected_input_ids"])
            rejected_labels.append(feature["rejected_labels"])
            rejected_attention_masks.append(feature["rejected_attention_mask"])
        template.post_image_token_process(chosen_input_ids, labels=chosen_labels, attention_mask=chosen_attention_masks,
                                          image_features=image_features, media_type=media_type, is_trainable=True)
        template.post_image_token_process(rejected_input_ids, labels=rejected_labels, attention_mask=rejected_attention_masks,
                                          image_features=image_features, media_type=media_type, is_trainable=True)
        for chosen_input_id, chosen_label, chosen_attention_mask, \
            rejected_input_id, rejected_label, rejected_attention_mask in \
            zip(chosen_input_ids, chosen_labels, chosen_attention_masks,
                rejected_input_ids,rejected_labels,rejected_attention_masks):
            new_features.append({
                "chosen_input_ids": chosen_input_id,
                "chosen_labels": chosen_label,
                "chosen_attention_mask": chosen_attention_mask,
                "rejected_input_ids": rejected_input_id,
                "rejected_labels": rejected_label,
                "rejected_attention_mask": rejected_attention_mask
            })
    template.post_image_features(image_features=image_features)
    return new_features

def get_sample_data(dataset, use_subprocess):
    if use_subprocess:
        dataloader = torch.utils.data.DataLoader(dataset, batch_size=1, num_workers=1, collate_fn=lambda x:x)
        sample_data = next(iter(dataloader))[0]
        del dataloader
        gc.collect()
        return sample_data
    else:
        return next(iter(dataset))

