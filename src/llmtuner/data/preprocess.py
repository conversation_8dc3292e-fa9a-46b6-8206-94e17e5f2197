# Copyright 2024 the LlamaFactory team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from copy import deepcopy
from functools import lru_cache, partial, wraps
from typing import TYPE_CHECKING, Any, Callable, Dict, List, Literal, Tuple

from ..extras.logging import get_logger
from .processors.feedback import preprocess_feedback_dataset
from .processors.pairwise import preprocess_pairwise_dataset, print_pairwise_dataset_example
from .processors.pretrain import preprocess_pretrain_dataset
from .processors.supervised import (
    preprocess_packed_supervised_dataset,
    preprocess_supervised_dataset,
    print_supervised_dataset_example,
)
from .processors.unsupervised import preprocess_unsupervised_dataset, print_unsupervised_dataset_example


if TYPE_CHECKING:
    from transformers import PreTrainedTokenizer

    from ..hparams import DataArguments
    from .template import Template

logger = get_logger(__name__)


def _media_sample_preprocess(media_token_id: int, input_ids: List[int], medias: List[Any]):
    """
    return:
        - used_medias
        - valid_sample
    """
    medias = medias if medias is not None else []
    media_count = input_ids.count(media_token_id)
    if media_count > len(medias):
        return [], False
    return medias[:media_count], True


# TODO: move this to each preprocess function
def multimodal_preprocess_wrapper(
    fn,
    template: "Template",
    input_ids_col="input_ids",
    drop_no_multimodal=True,
    stage="sft",
):
    @lru_cache()
    def get_media_token_id(key):
        tokenizer = template.tokenizer
        if key in ["image"]:
            media_token_id = tokenizer.convert_tokens_to_ids(template.image_token)
        elif key in ["video", "video_frames"]:
            media_token_id = tokenizer.convert_tokens_to_ids(template.video_token)
        elif key in ["audio"]:
            media_token_id = tokenizer.convert_tokens_to_ids(template.audio_token)
        else:
            raise Exception(f"unknown key: {key}")
        return media_token_id

    @wraps(fn)
    def wrapper(examples: Dict[str, List[Any]]):
        batch = fn(examples)

        if stage == "pt" and "labels" not in batch:
            batch["labels"] = deepcopy(batch[input_ids_col])
        input_ids = batch[input_ids_col]

        medias = {k: examples[k] for k in ["image", "video", "audio", "video_frames"] if k in examples}
        assert max(map(len, medias.values())) == len(input_ids) and min(map(len, medias.values())) == len(
            input_ids
        ), "MLLM training not support packing"

        new_batch = {k: [] for k in batch.keys()}
        used_medias = {k: [] for k in medias}

        for i in range(len(input_ids)):
            valid_sample = True
            used_media_count = 0
            sample_used_medias = {}
            sample_medias = {k: medias[k][i] for k in medias}
            if (
                "video_frames" in sample_medias
                and "video" in sample_medias
            ):
                if sample_medias["video_frames"] and sample_medias["video"]:
                    wrong_sample = {k: v[i] for k, v in examples.items()}
                    logger.warning(f"Found wrong sample: {wrong_sample}")
                    continue
                dropped_key = "video_frames" if sample_medias["video"] else "video"
                sample_medias.pop(dropped_key)

            # check valid sample and cut media tokens
            for k in sample_medias:
                media_token_id = get_media_token_id(k)
                used_media, valid_sample = _media_sample_preprocess(
                    media_token_id=media_token_id, input_ids=input_ids[i], medias=sample_medias[k]
                )
                if not valid_sample:
                    break
                used_media_count += len(used_media)
                sample_used_medias[k] = used_media

            if not valid_sample:
                wrong_sample = {k: v[i] for k, v in examples.items()}
                logger.warning(f"Found wrong sample: {wrong_sample}")
                continue
            # remove dropped images
            if drop_no_multimodal and used_media_count == 0:
                continue

            for k in used_medias:
                used_medias[k].append(sample_used_medias[k] if k in sample_used_medias else [])
            for k in batch:
                new_batch[k].append(batch[k][i])

        if sum(map(len, used_medias.values())) == 0:
            logger.warning("No available media data. Please check template and special tokens in your data.")

        new_batch.update(used_medias)
        return new_batch

    return wrapper


def get_preprocess_and_print_func(
    data_args: "DataArguments",
    stage: Literal["pt", "sft", "rm", "ppo", "kto"],
    template: "Template",
    tokenizer: "PreTrainedTokenizer",
    predict_with_generate: bool =False,
) -> Tuple[Callable, Callable]:
    if stage == "pt":
        preprocess_func = partial(
            preprocess_pretrain_dataset,
            tokenizer=tokenizer,
            data_args=data_args,
        )
        print_function = partial(print_unsupervised_dataset_example, tokenizer=tokenizer)
    elif stage == "sft" and not predict_with_generate:
        if data_args.packing:
            if data_args.neat_packing:
                from datasets.arrow_writer import OptimizedTypedSequence, TypedSequence

                def __init__(self, data, **kwargs):
                    return TypedSequence.__init__(
                        self,
                        data,
                        type=kwargs.pop("type", None),
                        try_type=kwargs.pop("try_type", None),
                        optimized_int_type=kwargs.pop("optimized_int_type", None),
                    )

                OptimizedTypedSequence.__init__ = __init__
            preprocess_func = partial(
                preprocess_packed_supervised_dataset,
                template=template,
                tokenizer=tokenizer,
                data_args=data_args,
            )
        else:
            preprocess_func = partial(
                preprocess_supervised_dataset,
                template=template,
                tokenizer=tokenizer,
                data_args=data_args,
            )

        print_function = partial(print_supervised_dataset_example, tokenizer=tokenizer)
    elif stage == "rm":
        preprocess_func = partial(
            preprocess_pairwise_dataset,
            template=template,
            tokenizer=tokenizer,
            data_args=data_args,
        )
        print_function = partial(print_pairwise_dataset_example, tokenizer=tokenizer)
    elif stage == "kto":
        preprocess_func = partial(
            preprocess_feedback_dataset,
            template=template,
            tokenizer=tokenizer,
            data_args=data_args,
        )
        print_function = partial(print_supervised_dataset_example, tokenizer=tokenizer)
    else:
        preprocess_func = partial(
            preprocess_unsupervised_dataset,
            template=template,
            tokenizer=tokenizer,
            data_args=data_args,
        )
        print_function = partial(print_unsupervised_dataset_example, tokenizer=tokenizer)

    if template.processor is not None:
        input_ids_col = "input_ids" if stage != "rm" else "chosen_input_ids"
        preprocess_func = multimodal_preprocess_wrapper(
            preprocess_func,
            template=template,
            input_ids_col=input_ids_col,
            drop_no_multimodal=data_args.drop_no_multimodal,
            stage=stage,
        )

    return preprocess_func, print_function
