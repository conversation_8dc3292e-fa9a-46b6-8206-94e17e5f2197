import json
import os
from functools import partial
from typing import TYPE_CHECKING, Any, Dict, List, Union

from ..extras.constants import AUDIO_PLACEHOLDER, IMAGE_PLACEHOLDER, VIDEO_PLACEHOLDER
from ..extras.logging import get_logger
from .utils import Role, get_sample_data


if TYPE_CHECKING:
    from datasets import Dataset, IterableDataset
    from transformers import Seq2SeqTrainingArguments

    from ..hparams import DataArguments
    from .parser import DatasetAttr
    from .template import Template


logger = get_logger(__name__)


def _convert_multimodal(multimodals: List[Any], multimodal_folder: str, dataset_attr: "DatasetAttr") -> List[Any]:
    r"""
    Optionally concatenates image path to dataset dir when loading from local disk.
    """
    if multimodals is None:
        return []
    if dataset_attr.dataset_name.startswith('ailake'):
        multimodals = multimodals.split(b'---nebula_sql_ailake_image_split_flags---')

    if not isinstance(multimodals, list):
        multimodals = [multimodals]
    elif len(multimodals) == 0:
        return []
    else:
        multimodals = multimodals[:]

    def convert_path(obj: Any) -> Any:
        if (
            multimodal_folder is not None
            and isinstance(obj, str)
            and not obj.startswith(("http://", "https://"))
        ):
            return os.path.join(multimodal_folder, obj)
        else:
            return obj

    if dataset_attr.load_from in ["script", "file"]:
        for i in range(len(multimodals)):
            if isinstance(multimodals[i], list):
                multimodals[i] = [convert_path(o) for o in multimodals[i]]
            else:
                multimodals[i] = convert_path(multimodals[i])

    return multimodals


def convert_alpaca(
    examples: Dict[str, List[Any]], dataset_attr: "DatasetAttr", data_args: "DataArguments"
) -> Dict[str, List[Any]]:
    r"""
    Converts alpaca format dataset to the standard format.
    """
    outputs = {"prompt": [], "response": [], "system": [], "tools": [], "image": [], "video": [], "video_frames": [], "audio": []}
    convert_images = partial(_convert_multimodal, dataset_attr=dataset_attr, multimodal_folder=data_args.image_folder)
    convert_videos = partial(_convert_multimodal, dataset_attr=dataset_attr, multimodal_folder=data_args.video_folder)
    convert_audios = partial(_convert_multimodal, dataset_attr=dataset_attr, multimodal_folder=data_args.audio_folder)
    for i in range(len(examples[dataset_attr.prompt])):
        prompt = []
        if dataset_attr.history and isinstance(examples[dataset_attr.history][i], list):
            for old_prompt, old_response in examples[dataset_attr.history][i]:
                prompt.append({"role": Role.USER.value, "content": old_prompt})
                prompt.append({"role": Role.ASSISTANT.value, "content": old_response})

        content = []
        if dataset_attr.prompt and examples[dataset_attr.prompt][i]:
            content.append(examples[dataset_attr.prompt][i])

        if dataset_attr.query and examples[dataset_attr.query][i]:
            content.append(examples[dataset_attr.query][i])

        prompt.append({"role": Role.USER.value, "content": "\n".join(content)})  # "prompt\nquery"

        if dataset_attr.kto_tag and isinstance(examples[dataset_attr.kto_tag][i], bool):  # kto example
            response = [{"role": Role.ASSISTANT.value, "content": examples[dataset_attr.response][i]}]
            if examples[dataset_attr.kto_tag][i]:
                response = response + [{"role": Role.ASSISTANT.value, "content": ""}]
            else:
                response = [{"role": Role.ASSISTANT.value, "content": ""}] + response
        elif (
            dataset_attr.ranking
            and isinstance(examples[dataset_attr.chosen][i], str)
            and isinstance(examples[dataset_attr.rejected][i], str)
        ):  # pairwise example
            response = [
                {"role": Role.ASSISTANT.value, "content": examples[dataset_attr.chosen][i]},
                {"role": Role.ASSISTANT.value, "content": examples[dataset_attr.rejected][i]},
            ]
        elif dataset_attr.response and isinstance(examples[dataset_attr.response][i], (int, str)):  # normal example
            response = [{"role": Role.ASSISTANT.value, "content": str(examples[dataset_attr.response][i])}]
        else:  # unsupervised
            response = []

        outputs["prompt"].append(prompt)
        outputs["response"].append(response)
        outputs["system"].append(examples[dataset_attr.system][i] if dataset_attr.system else "")
        outputs["tools"].append(examples[dataset_attr.tools][i] if dataset_attr.tools else "")
        outputs["image"].append(convert_images(examples[dataset_attr.image][i]) if dataset_attr.image else [])
        outputs["video"].append(convert_videos(examples[dataset_attr.video][i]) if dataset_attr.video else [])
        outputs["video_frames"].append(
            convert_videos(examples[dataset_attr.video_frames][i]) if dataset_attr.video_frames else []
        )
        outputs["audio"].append(convert_audios(examples[dataset_attr.audio][i]) if dataset_attr.audio else [])

    if not dataset_attr.image:
        outputs.pop("image")
    if not dataset_attr.video:
        outputs.pop("video")
    if not dataset_attr.audio:
        outputs.pop("audio")
    if not dataset_attr.video_frames:
        outputs.pop("video_frames")
    return outputs


def convert_sharegpt(
    examples: Dict[str, List[Any]], dataset_attr: "DatasetAttr", data_args: "DataArguments"
) -> Dict[str, List[Any]]:
    r"""
    Converts sharegpt format dataset to the standard format.
    """
    outputs = {"prompt": [], "response": [], "system": [], "tools": [], "image": [], "video": [], "video_frames": [], "audio": []}
    convert_images = partial(_convert_multimodal, dataset_attr=dataset_attr, multimodal_folder=data_args.image_folder)
    convert_videos = partial(_convert_multimodal, dataset_attr=dataset_attr, multimodal_folder=data_args.video_folder)
    convert_audios = partial(_convert_multimodal, dataset_attr=dataset_attr, multimodal_folder=data_args.audio_folder)
    tag_mapping = {
        dataset_attr.user_tag: Role.USER.value,
        dataset_attr.assistant_tag: Role.ASSISTANT.value,
        dataset_attr.observation_tag: Role.OBSERVATION.value,
        dataset_attr.function_tag: Role.FUNCTION.value,
        dataset_attr.system_tag: Role.SYSTEM.value,
    }
    odd_tags = (dataset_attr.user_tag, dataset_attr.observation_tag)
    even_tags = (dataset_attr.assistant_tag, dataset_attr.function_tag)
    accept_tags = (odd_tags, even_tags)
    for i, messages in enumerate(examples[dataset_attr.messages]):
        if isinstance(messages, str):
            messages = json.loads(messages)
        if dataset_attr.system_tag and messages[0][dataset_attr.role_tag] == dataset_attr.system_tag:
            system = messages[0][dataset_attr.content_tag]
            messages = messages[1:]
        else:
            system = examples[dataset_attr.system][i] if dataset_attr.system else ""

        if len(messages) == 0:
            continue

        aligned_messages = []
        broken_data = False
        for turn_idx, message in enumerate(messages):
            if message[dataset_attr.role_tag] not in accept_tags[turn_idx % 2]:
                logger.warning("Invalid role tag in {}.".format(messages))
                broken_data = True

            aligned_messages.append(
                {"role": tag_mapping[message[dataset_attr.role_tag]], "content": message[dataset_attr.content_tag]}
            )

        if (not dataset_attr.ranking and len(aligned_messages) % 2 != 0) or (
            dataset_attr.ranking and len(aligned_messages) % 2 == 0
        ):
            logger.warning("Invalid message count in {}.".format(messages))
            broken_data = True

        if dataset_attr.kto_tag and isinstance(examples[dataset_attr.kto_tag][i], bool):  # kto example
            prompt = aligned_messages[:-1]
            response = aligned_messages[-1:]
            if examples[dataset_attr.kto_tag][i]:
                response = response + [{"role": Role.ASSISTANT.value, "content": ""}]
            else:
                response = [{"role": Role.ASSISTANT.value, "content": ""}] + response
        elif (
            dataset_attr.ranking
            and isinstance(examples[dataset_attr.chosen][i], dict)
            and isinstance(examples[dataset_attr.rejected][i], dict)
        ):  # pairwise example
            chosen = examples[dataset_attr.chosen][i]
            rejected = examples[dataset_attr.rejected][i]
            if (
                chosen[dataset_attr.role_tag] not in accept_tags[-1]
                or rejected[dataset_attr.role_tag] not in accept_tags[-1]
            ):
                logger.warning("Invalid role tag in {}.".format([chosen, rejected]))
                broken_data = True

            prompt = aligned_messages
            response = [
                {"role": tag_mapping[chosen[dataset_attr.role_tag]], "content": chosen[dataset_attr.content_tag]},
                {"role": tag_mapping[rejected[dataset_attr.role_tag]], "content": rejected[dataset_attr.content_tag]},
            ]
        else:  # normal example
            prompt = aligned_messages[:-1]
            response = aligned_messages[-1:]

        if broken_data:
            logger.warning("Skipping this abnormal example.")
            continue

        outputs["prompt"].append(prompt)
        outputs["response"].append(response)
        outputs["system"].append(system)
        outputs["tools"].append(examples[dataset_attr.tools][i] if dataset_attr.tools else "")
        outputs["image"].append(convert_images(examples[dataset_attr.image][i]) if dataset_attr.image else [])
        outputs["video"].append(convert_videos(examples[dataset_attr.video][i]) if dataset_attr.video else [])
        outputs["video_frames"].append(
            convert_videos(examples[dataset_attr.video_frames][i]) if dataset_attr.video_frames else []
        )
        outputs["audio"].append(convert_audios(examples[dataset_attr.audio][i]) if dataset_attr.audio else [])

    if not dataset_attr.image:
        outputs.pop("image")
    if not dataset_attr.video:
        outputs.pop("video")
    if not dataset_attr.audio:
        outputs.pop("audio")
    if not dataset_attr.video_frames:
        outputs.pop("video_frames")
    return outputs


def convert_placeholder_token(example: Dict[str, Any], template: "Template"):
    def replace_token(text: str):
        if template.image_token and "image" in example:
            text = text.replace(IMAGE_PLACEHOLDER, template.image_token)
        if template.video_token and ("video" in example or "video_frames" in example):
            text = text.replace(VIDEO_PLACEHOLDER, template.video_token)
        if template.audio_token and "audio" in example:
            text = text.replace(AUDIO_PLACEHOLDER, template.audio_token)
        return text

    def replace_content_list(content_list: List[Dict[str, str]]):
        return [{"role": content["role"], "content": replace_token(content["content"])} for content in content_list]

    example["prompt"] = replace_content_list(example["prompt"])
    example["response"] = replace_content_list(example["response"])
    example["system"] = replace_token(example["system"])
    example["tools"] = replace_token(example["tools"])
    return example


def align_dataset(
    dataset: Union["Dataset", "IterableDataset"],
    dataset_attr: "DatasetAttr",
    data_args: "DataArguments",
    training_args: "Seq2SeqTrainingArguments",
    template: "Template",
) -> Union["Dataset", "IterableDataset"]:
    r"""
    Aligned dataset:
        prompt: [{"role": "user", "content": "..."}] * (2T - 1)
        response: [{"role": "assistant", "content": "..."}] * N (N > 1 for ranking dataset)
        system: "..."
        tools: "..."
    """
    if dataset_attr.formatting == "alpaca":
        convert_func = partial(convert_alpaca, dataset_attr=dataset_attr, data_args=data_args)
    else:
        convert_func = partial(convert_sharegpt, dataset_attr=dataset_attr, data_args=data_args)

    sample_data = get_sample_data(dataset=dataset, use_subprocess=True)
    column_names = set(sample_data.keys())
    feature_keys = {"prompt", "response", "system", "tools"}
    if dataset_attr.image:
        feature_keys.add("image")
    if dataset_attr.video:
        feature_keys.add("video")
    if dataset_attr.audio:
        feature_keys.add("audio")
    if dataset_attr.video_frames:
        feature_keys.add("video_frames")
    kwargs = {}
    if not data_args.streaming:
        kwargs = dict(
            num_proc=data_args.preprocessing_num_workers,
            load_from_cache_file=(not data_args.overwrite_cache) or (training_args.local_process_index != 0),
            desc="Converting format of dataset",
        )

    dataset = dataset.map(
        convert_func,
        batched=True,
        remove_columns=list(column_names - feature_keys),
        **kwargs,
    )
    if "desc" in kwargs:
        kwargs["desc"] = "Replacing Placeholder tokens"
    dataset = dataset.map(partial(convert_placeholder_token, template=template), batched=False, **kwargs)
    return dataset
