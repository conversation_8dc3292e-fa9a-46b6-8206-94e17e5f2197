import gc
import os
from typing import TYPE_CHECKING, Dict, <PERSON><PERSON>

import torch
from openlm_hub import repo_download
from transformers import InfNanRemoveLogitsProcessor, LogitsProcessorList
from transformers.utils import (
    is_torch_bf16_gpu_available,
    is_torch_cuda_available,
    is_torch_mps_available,
    is_torch_npu_available,
    is_torch_xpu_available,
)

from .logging import get_logger


_is_fp16_available = is_torch_npu_available() or is_torch_cuda_available()
try:
    _is_bf16_available = is_torch_bf16_gpu_available()
except Exception:
    _is_bf16_available = False


if TYPE_CHECKING:
    from llmtuner.hparams import ModelArguments

    from ..generate.infer_args import InferArguments
    from ..hparams.data_args import DataArguments
    from ..hparams.finetuning_args import FinetuningArguments


logger = get_logger(__name__)


class AverageMeter:
    r"""
    Computes and stores the average and current value.
    """

    def __init__(self):
        self.reset()

    def reset(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0

    def update(self, val, n=1):
        self.val = val
        self.sum += val * n
        self.count += n
        self.avg = self.sum / self.count


def count_parameters(model: torch.nn.Module) -> Tuple[int, int]:
    r"""
    Returns the number of trainable parameters and number of all parameters in the model.
    """
    trainable_params, all_param = 0, 0
    for param in model.parameters():
        num_params = param.numel()
        # if using DS Zero 3 and the weights are initialized empty
        if num_params == 0 and hasattr(param, "ds_numel"):
            num_params = param.ds_numel

        # Due to the design of 4bit linear layers from bitsandbytes, multiply the number of parameters by 2
        if param.__class__.__name__ == "Params4bit":
            num_params = num_params * 2

        all_param += num_params
        if param.requires_grad:
            trainable_params += num_params

    return trainable_params, all_param


def get_current_device() -> torch.device:
    r"""
    Gets the current available device.
    """
    if is_torch_xpu_available():
        device = "xpu:{}".format(os.environ.get("LOCAL_RANK", "0"))
    elif is_torch_npu_available():
        device = "npu:{}".format(os.environ.get("LOCAL_RANK", "0"))
    elif is_torch_mps_available():
        device = "mps:{}".format(os.environ.get("LOCAL_RANK", "0"))
    elif is_torch_cuda_available():
        device = "cuda:{}".format(os.environ.get("LOCAL_RANK", "0"))
    else:
        device = "cpu"

    return torch.device(device)


def get_device_count() -> int:
    return torch.cuda.device_count()


def get_logits_processor() -> "LogitsProcessorList":
    r"""
    Gets logits processor that removes NaN and Inf logits.
    """
    logits_processor = LogitsProcessorList()
    logits_processor.append(InfNanRemoveLogitsProcessor())
    return logits_processor


def infer_optim_dtype(model_dtype: torch.dtype) -> torch.dtype:
    r"""
    Infers the optimal dtype according to the model_dtype and device compatibility.
    """
    if _is_bf16_available and model_dtype == torch.bfloat16:
        return torch.bfloat16
    elif _is_fp16_available:
        return torch.float16
    else:
        return torch.float32


def torch_gc() -> None:
    r"""
    Collects GPU memory.
    """
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()


def try_download_model_from_ms(model_args: "ModelArguments") -> None:
    if not use_modelscope() or os.path.exists(model_args.model_name_or_path):
        return

    try:
        from modelscope import snapshot_download

        revision = "master" if model_args.model_revision == "main" else model_args.model_revision
        model_args.model_name_or_path = snapshot_download(
            model_args.model_name_or_path, revision=revision, cache_dir=model_args.cache_dir
        )
    except ImportError:
        raise ImportError("Please install modelscope via `pip install modelscope -U`")


def use_modelscope() -> bool:
    return bool(int(os.environ.get("USE_MODELSCOPE_HUB", "0")))


def down_model(uri, used_for=None):
    return repo_download(
        uri,
        ignore_patterns=["global_step*/*_states.pt", "*iter_0000001/dist_optimizer/*"],
        repo_meta={"usage": used_for},
        use_subprocess=True,
    )


def get_dataset_type(path: str):
    if path.startswith("/data/oss_bucket"):
        type = "oss"
    elif path.startswith("odps://"):
        type = "odps"
    elif os.path.isfile(path):
        type = "local_file"
    else:
        type = "unknown"
    return type

def log_meta_to_openlm(
    data_args: "DataArguments" = None,
    finetuning_args: "FinetuningArguments" = None,
    infer_args: "InferArguments" = None,
):
    try:
        from openlm_hub import log_meta, sync_meta, DatasetMeta, DataLineageMeta, ModelArtifactMeta

        # only for parse single dataset
        # TODO for parse multi dataset
        # data_args for train job
        if data_args:
            dataset_metas = []
            if data_args.file_name or data_args.dataset_name:
                if data_args.dataset_name:
                    dataset_metas.append(
                        DatasetMeta(
                            name=data_args.dataset_name,
                            path=data_args.dataset_name,
                            usage="train",
                            info={"subset": data_args.subset},
                            type="openlm",
                        )
                    )
                else:
                    dataset_metas.append(
                        DatasetMeta(
                            name=data_args.file_name,
                            path=data_args.file_name,
                            usage="train",
                            type=get_dataset_type(data_args.file_name),
                        )
                    )
            if data_args.eval_dataset_name or data_args.eval_file_name:
                if data_args.eval_dataset_name:
                    dataset_metas.append(
                        DatasetMeta(
                            name=data_args.eval_dataset_name,
                            path=data_args.eval_dataset_name,
                            usage="eval",
                            type="openlm",
                        )
                    )
                else:
                    dataset_metas.append(
                        DatasetMeta(
                            name=data_args.eval_file_name,
                            path=data_args.eval_file_name,
                            usage="eval",
                            type=get_dataset_type(data_args.eval_file_name),
                        )
                    )
            log_meta(
                meta=DataLineageMeta(
                    datasets=dataset_metas,
                    model_artifact=ModelArtifactMeta(
                        type=finetuning_args.finetuning_type, stage=finetuning_args.stage
                    ),
                    extra_metas={
                        "use_turbo": finetuning_args.use_turbo,
                        "task_mode": "train",
                    },
                )
            )
        # infer_args for offline infer
        if infer_args:
            input_data = []
            output_data = []
            if infer_args.inputs:
                if infer_args.tables:
                    input_data.append(
                        DatasetMeta(
                            name=infer_args.inputs,
                            path=infer_args.inputs,
                            usage="input",
                            info={
                                "prompt_column": infer_args.prompt_column,
                                "image_column": infer_args.image_column,
                                "system_column": infer_args.system_column,
                            },
                            type="odps",
                        )
                    )
                else:
                    if infer_args.load_from == "file":
                        input_data.append(
                            DatasetMeta(
                                name=infer_args.inputs,
                                path=infer_args.inputs,
                                usage="input",
                                info={
                                    "prompt_column": infer_args.prompt_column,
                                    "image_column": infer_args.image_column,
                                    "system_column": infer_args.system_column,
                                },
                                type=get_dataset_type(infer_args.inputs),
                            )
                        )
                    elif infer_args.load_from == "openlm":
                        # TODO generate has not support openlm to infer
                        pass
            if infer_args.outputs:
                if infer_args.tables:
                    output_data.append(
                        DatasetMeta(
                            name=infer_args.outputs,
                            path=infer_args.outputs,
                            usage="output",
                            type="odps",
                        )
                    )
                elif infer_args.load_from == 'file':
                    output_data.append(
                        DatasetMeta(
                            name=infer_args.outputs,
                            path=infer_args.outputs,
                            usage="output",
                            type=get_dataset_type(infer_args.outputs),
                        )
                    )
                elif infer_args.load_from == "openlm":
                    # TODO generate has not support openlm to infer
                    pass
            log_meta(
                meta=DataLineageMeta(
                    datasets=input_data,
                    extra_metas={
                        "use_turbo": finetuning_args.use_turbo,
                        "task_mode": "offline_infer",
                        "infer_mode": infer_args.infer_mode
                    },
                    output_datasets=output_data
                )
            )
        sync_meta()
    except Exception:
        logger.exception("log openlm-hub lineage failed,ignore this error.")


def is_float_tensor(tensor):
    if not isinstance(tensor, torch.Tensor):
        return False
    return tensor.dtype in (torch.float, torch.float16, torch.bfloat16)


def recursive_converter(converter, value):
    if isinstance(value, list):
        new_value = []
        for v in value:
            new_value += [recursive_converter(converter, v)]
        return new_value
    else:
        return converter(value)


def to_device(data: Dict, device: torch.device):
    def cast_tensor(v):
        if isinstance(v, torch.Tensor):
            return v.to(device=device)
        else:
            return v

    new_data = {}
    for k, v in data.items():
        new_data[k] = recursive_converter(cast_tensor, v)
    return new_data


def set_odps_table_reader_mode():
    logger.info("set odps table reader to TunnelReader")
    import common_io
    common_io.table.TableReader = common_io.table_tunnel.TableTunnelReader
