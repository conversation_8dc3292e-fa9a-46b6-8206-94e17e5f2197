from typing import TYPE_CHECKING, Optional

from ..data.template import get_template_and_fix_tokenizer
from ..extras.logging import get_logger
from ..extras.misc import count_parameters
from ..extras.packages import is_turbo_available
from .loader import load_tokenizer


if is_turbo_available():
    from transformers_turbo.models import AutoModel

if TYPE_CHECKING:
    from transformers import PreTrainedTokenizer, TrainingArguments

    from ..hparams import FinetuningArguments, ModelArguments


logger = get_logger(__name__)


def load_turbo_model(
    tokenizer: "PreTrainedTokenizer",  # TODO: support resize embedding
    training_args: "TrainingArguments",
    model_args: "ModelArguments",
    finetuning_args: "FinetuningArguments",
    is_trainable: Optional[bool] = False,  # TODO: support ref model
    add_valuehead: Optional[bool] = False,  # TODO: support reward model
):
    model = AutoModel.from_pretrained(model_args.model_name_or_path, training_args)
    if finetuning_args.finetuning_type == "freeze" and finetuning_args.freeze_module_prefix is not None:
        for name, param in model.named_parameters():
            if any(name.startswith(prefix) for prefix in finetuning_args.freeze_module_prefix):
                param.requires_grad_(False)
    trainable_params, all_param = count_parameters(model)
    if is_trainable:
        param_stats = "trainable params: {:d} || all params: {:d} || trainable%: {:.4f}".format(
            trainable_params, all_param, 100 * trainable_params / all_param
        )
    else:
        param_stats = "all params: {:d}".format(all_param)
    logger.info(param_stats)
    return model


def load_turbo_model_and_template(
    training_args: "TrainingArguments",
    model_args: "ModelArguments",
    finetuning_args: "FinetuningArguments",
    template: Optional[str] = None,
    is_trainable: Optional[bool] = False,
    add_valuehead: Optional[bool] = False,
):
    tokenizer, processor = load_tokenizer(model_args)
    template = get_template_and_fix_tokenizer(tokenizer, name=template, processor=processor)
    model = load_turbo_model(tokenizer, training_args, model_args, finetuning_args, is_trainable, add_valuehead)
    return model, template
