{"job_name": "EditorV2bs8cut8K-2025-05-13-10-23-39", "training_type": "pytorch", "docker_image": "reg.docker.alibaba-inc.com/alimama/xdl:pytorch_alpha", "script": "", "worker": {"instance_num": 128, "cpu_cores": 8.0, "gpu_cores": 1.0, "memory_m": 80000}, "ps": {"instance_num": 0, "cpu_cores": 4, "gpu_cores": 1, "memory_m": 40000}, "reader": {"data_dir": "./data"}, "checkpoint": {"output_dir": "./ckpt"}, "meta_dir": "./meta", "parameter": {"worker": {"user_define_cmd": "src/train_bash.py --stage sft     --model_name_or_path=/data/xpfs_0/data/model/Qwen3-8B-Base/     --do_train     --dataset=sft-inner-fim    --prompt=instruction     --query=input     --response=output     --template=empty     --finetuning_type full     --output_dir=/data/xpfs_0/data/model/qwen_3_8B_0512_sft/     --overwrite_cache     --per_device_train_batch_size 4     --per_device_eval_batch_size 1     --gradient_accumulation_steps 1     --lr_scheduler_type cosine     --logging_steps 1     --save_steps 100     --num_train_epochs 2     --learning_rate=5e-5     --packing False     --cutoff_len=4096     --preprocessing_num_workers=8     --warmup_ratio 0.15     --dataloader_num_workers=4     --plot_loss     --deepspeed=scripts/ds_zero3.json     --bf16"}}, "min_finish_worker_rate": 100, "oss_access_id": "LTAIGcY7idsIs0lm", "oss_access_key": "KM7z2PawYxDLqZWIFk7VraBUlZyBLk", "oss_bucket": "mtl4-ai-resources-daily", "oss_endpoint": "oss-cn-hangzhou-internal.aliyuncs.com", "job_success_notice": "true", "algo_name": "pytorch220", "scheduler_queue": "taobao_dev_mtl"}