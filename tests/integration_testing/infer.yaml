task_submit_setting:
  # 星云任务的job 名字，建议填写有实际含义的
  job_name: tuningfactory_integration_test_for_infer
  # 任务队列，用户可以填写自己的队列
  scheduler_queue: ''
  # 训练任务总gpu数，即pytorch的world_size
  world_size: 1
  # gpu卡数，只在非训练场景中使用，表示单worker可用的gpu卡数,如需要请填写<=8的数字
  # 如world_size=1,gpu_cores=4,表示申请一个worker，worker可用4张卡，一般用在tp并行推理上
  gpu_cores: 1
  # 容器的环境变量，多个都逗号分隔，如ODPS_TUNNEL_MODE=1,EIP_SLICE_SIZE=10
  env: ""
  oss_access_id: ""
  # 如果挂载oss,填写oss的密码即AccessKeySecret，不填会自动忽略
  oss_access_key: ""
  # 如果挂载oss,填写oss的bucket,即 abc 这种名字，不要填写oss://xxx。不填会自动忽略
  # 挂载成功oss://bucket/下的所有文件都会在/data/oss_bucket_0/下面
  oss_bucket: ""
  # 如果挂载oss,填写oss的endpoint，不需要以http开头 例如oss-cn-hangzhou-zmf.aliyuncs.com
  oss_endpoint: ""
  # nas 挂载点id如example.nas.aliyuncs.com:/tmp/test
  # 如果挂载nas,填写nas的id,不填会自动忽略。挂载成功/tmp/test目录下文件会出现/mnt/workspace目录下
  nas_file_system_id: ""
  # 任务成功后是否发送钉钉通知，默认不发送
  job_success_notice: false
  # pytorch版本，请不要修改。如需要修改，请找MDL值班同学
  algo_name:
  min_finish_worker_rate: 100
  # 只在推理场景中使用，表示允许推理单个worker最大失败次数
  max_failover_times: 10
  # 默认的代码运行脚本，请不要修改
  entry: src/generate.py
  # 平台内置参数，请不要删除，否则会报错
  openlm_job_type: predict
  # Openlm项目名，表示任务所属的项目,星云项目和Openlm项目是一个概念。不填会默认使用命令行登录配置中的项目
  project: ""
llm_running_param:
  # 基本参数,请仔细阅读注释并按需修改
  basic:
    # 模型加载路径，可以为huggingface 模型名（可以在openlm 模型库文件列表中复制过来），或者自己训练产出的mos 地址，或者oss 挂载地址
    model_name_or_path:
    # lora模型的加载路径。如果不加载lora模型，忽略即可
    # 一般在model_name_or_path填写基座模型，adapter_name_or_path填写自己的lora的模型。mos存储，填写mos uri即可
    adapter_name_or_path: ""
    # 模型的模版，用来生成prompt。该参数必填，如果为空，请根据文档填写合适的模版即可
    template:
    # 推理场景中，表示输入的数据集的路径
    # 如odps表，oss文件,oss挂载填写/data/oss_bucket_0/你的文件，odps填写odps://odps项目/tables/你的表/ds=xxx
    inputs: ""
    # 推理场景中，表示输出的数据集的路
    # 如odps表，oss文件,oss挂载填写/data/oss_bucket_0/你的文件，odps填写odps://odps项目/tables/你的表/ds=xxx
    outputs: ""
    # 推理场景中，表示输入的列名，根据自己的数据集按需填写
    prompt_column: 0
    # 数据集的列表，在MLLM中，表示图片数据所在的列
    image: ""
    # 数据集的列表，在MLLM中，表示视频数据所在的列
    video: ""
    # 搭配image 参数使用，用于图片路径前缀，填写后会使用 os.path.join(image_folder,image) 来获取图片的地址，image为http地址或者/开头的绝对路径时可不填
    image_folder: ""
    # 搭配video 参数使用，用于视频路径前缀，填写后会使用 os.path.join(video_folder,video) 来获取视频的地址，http地址或者/开头的绝对路径时可不填
    video_folder: ""
  # 扩展参数，一般不用修改，如想了解具体含义并修改，请查看文档 https://aliyuque.antfin.com/uxctvg/yd0939/qaorgu7fp1mhs8p1
  extended:
    batch_size: 1
    do_sample: false
    temperature: 1.0
    top_p: 0.8
    top_k: 100
    seed: 1
    cutoff_len: 1024
    max_new_tokens: 512
    # 推理场景中，表示推理的方式，default为huggingface默认的generate,可以填写vllm,vllm-async
    infer_mode: default
  # 框架自带的参数，不建议修改
  build-in:
    load_from: file
