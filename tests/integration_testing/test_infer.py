from openlm_hub import TaskConfigManager, TaskManager


class InferTest:
    def __init__(self, commit_id, openlm_token):
        self.openlm_token = openlm_token
        self.commit_id = commit_id
        with open("./infer.yaml", "r") as f:
            self.infer_basic_config = TaskConfigManager(openlm_token=openlm_token).to_dict(f.read())

    def test_llm_oss_infer_default(self) -> TaskManager:
        config = self.infer_basic_config.copy()
        config["task_submit_setting"]["llm_code_commit_id"] = self.commit_id
        config["llm_running_param"]["basic"]["model_name_or_path"] = 'Qwen/Qwen2.5-7B-Instruct'
        config["llm_running_param"]["basic"]["template"] = 'qwen2_5'
        config["llm_running_param"]["basic"]["inputs"] = '/data/oss_bucket_0/shidie/tests/data/qwen2d5_infer_default_data.json'
        config["llm_running_param"]["basic"]["outputs"] = '/data/oss_bucket_0/shidie/tests/outputs/qwen2d5_infer_default_data_output.json'
        config["llm_running_param"]["basic"]["prompt_column"] = 'content'
        config["llm_running_param"]["extended"]["infer_mode"] = 'default'
        config["llm_running_param"]["extended"]["flash_attn"] = 'fa2'
        task_manager = TaskManager(openlm_token=self.openlm_token)
        task_manager.run(task_config=config, access_id="xxx", access_key="xxx")
        return task_manager
    
    def test_llm_oss_infer_vllm(self) -> TaskManager:
        config = self.infer_basic_config.copy()
        config["task_submit_setting"]["llm_code_commit_id"] = self.commit_id
        config["llm_running_param"]["basic"]["model_name_or_path"] = 'Qwen/Qwen2.5-7B-Instruct'
        config["llm_running_param"]["basic"]["template"] = 'qwen2_5'
        config["llm_running_param"]["basic"]["inputs"] = '/data/oss_bucket_0/shidie/tests/data/qwen2d5_infer_vllm_data.json'
        config["llm_running_param"]["basic"]["outputs"] = '/data/oss_bucket_0/shidie/tests/outputs/qwen2d5_infer_vllm_data_output.json'
        config["llm_running_param"]["basic"]["prompt_column"] = 'content'
        config["llm_running_param"]["extended"]["infer_mode"] = 'vllm'
        config["llm_running_param"]["extended"]["flash_attn"] = 'fa2'
        task_manager = TaskManager(openlm_token=self.openlm_token)
        task_manager.run(task_config=config, access_id="xxx", access_key="xxx")
        return task_manager
    def test_mllm_oss_infer_default(self) -> TaskManager:
        config = self.infer_basic_config.copy()
        config["task_submit_setting"]["llm_code_commit_id"] = self.commit_id
        config["llm_running_param"]["basic"]["model_name_or_path"] = 'Qwen/Qwen2-VL-7B-Instruct'
        config["llm_running_param"]["basic"]["template"] = 'qwen2-vl'
        config["llm_running_param"]["basic"]["inputs"] = '/data/oss_bucket_0/shidie/tests/data/qwen2vl_infer_default_data.json'
        config["llm_running_param"]["basic"]["outputs"] = '/data/oss_bucket_0/shidie/tests/outputs/qwen2vl_infer_default_data_output.json'
        config["llm_running_param"]["basic"]["prompt_column"] = 'instruction'
        config["llm_running_param"]["basic"]["image"] = 'image'
        config["llm_running_param"]["basic"]["image_folder"] = '/checkpoint/binary/train_package/'
        config["llm_running_param"]["extended"]["infer_mode"] = 'default'
        config["llm_running_param"]["extended"]["flash_attn"] = 'sdpa'
        task_manager = TaskManager(openlm_token=self.openlm_token)
        task_manager.run(task_config=config, access_id="xxx", access_key="xxx")
        return task_manager
    
    def test_mllm_oss_infer_vllm(self) -> TaskManager:
        config = self.infer_basic_config.copy()
        config["task_submit_setting"]["llm_code_commit_id"] = self.commit_id
        config["llm_running_param"]["basic"]["model_name_or_path"] = 'Qwen/Qwen2-VL-7B-Instruct'
        config["llm_running_param"]["basic"]["template"] = 'qwen2-vl'
        config["llm_running_param"]["basic"]["inputs"] = '/data/oss_bucket_0/shidie/tests/data/qwen2vl_infer_vllm_data.json'
        config["llm_running_param"]["basic"]["outputs"] = '/data/oss_bucket_0/shidie/tests/outputs/qwen2vl_infer_vllm_data_output.json'
        config["llm_running_param"]["basic"]["prompt_column"] = 'instruction'
        config["llm_running_param"]["basic"]["image"] = 'image'
        config["llm_running_param"]["basic"]["image_folder"] = '/checkpoint/binary/train_package/'
        config["llm_running_param"]["extended"]["infer_mode"] = 'vllm'
        config["llm_running_param"]["extended"]["flash_attn"] = 'sdpa'
        task_manager = TaskManager(openlm_token=self.openlm_token)
        task_manager.run(task_config=config, access_id="xxx", access_key="xxx")
        return task_manager

    def test_llm_odps_infer_single_gpu(self) -> TaskManager:
        odps_infer_single_gpu_config = self.infer_basic_config.copy()
        odps_infer_single_gpu_config["task_submit_setting"]["llm_code_commit_id"] = self.commit_id
        odps_infer_single_gpu_config["task_submit_setting"]["project"] = "项目"
        odps_infer_single_gpu_config["task_submit_setting"]["scheduler_queue"] = "队列"
        odps_infer_single_gpu_config["task_submit_setting"]["world_size"] = "4"
        odps_infer_single_gpu_config["task_submit_setting"]["algo_name"] = "pytorch240"
        odps_infer_single_gpu_config["llm_running_param"]["basic"]["model_name_or_path"] = "xxx"
        odps_infer_single_gpu_config["llm_running_param"]["basic"]["template"] = "xxx"
        odps_infer_single_gpu_config["llm_running_param"]["basic"]["inputs"] = "xxx"
        odps_infer_single_gpu_config["llm_running_param"]["basic"]["outputs"] = "xxx"
        odps_infer_single_gpu_config["llm_running_param"]["basic"]["prompt_column"] = "xxx"
        odps_infer_single_gpu_config["llm_running_param"]["extended"]["infer_mode"] = "vllm"
        task_manager = TaskManager(openlm_token=self.openlm_token)
        task_manager.run(task_config=odps_infer_single_gpu_config, access_id="xx", access_key="xxx")
        return task_manager

    def test_llm_odps_infer_multi_gpu(self) -> TaskManager:
        odps_infer_single_gpu_config = self.infer_basic_config.copy()
        odps_infer_single_gpu_config["task_submit_setting"]["llm_code_commit_id"] = self.commit_id
        odps_infer_single_gpu_config["task_submit_setting"]["project"] = "项目"
        odps_infer_single_gpu_config["task_submit_setting"]["scheduler_queue"] = "队列"
        odps_infer_single_gpu_config["task_submit_setting"]["world_size"] = "1"
        odps_infer_single_gpu_config["task_submit_setting"]["gpu_cores"] = "4"
        odps_infer_single_gpu_config["task_submit_setting"]["algo_name"] = "pytorch240"
        odps_infer_single_gpu_config["llm_running_param"]["basic"]["model_name_or_path"] = "xxx"
        odps_infer_single_gpu_config["llm_running_param"]["basic"]["template"] = "xxx"
        odps_infer_single_gpu_config["llm_running_param"]["basic"]["inputs"] = "xxx"
        odps_infer_single_gpu_config["llm_running_param"]["basic"]["outputs"] = "xxx"
        odps_infer_single_gpu_config["llm_running_param"]["basic"]["prompt_column"] = "xxx"
        odps_infer_single_gpu_config["llm_running_param"]["extended"]["infer_mode"] = "vllm"
        task_manager = TaskManager(openlm_token=self.openlm_token)
        task_manager.run(task_config=odps_infer_single_gpu_config, access_id="xx", access_key="xxx")
        return task_manager

    def test_mllm_odps_infer_single_gpu(self) -> TaskManager:
        odps_infer_single_gpu_config = self.infer_basic_config.copy()
        odps_infer_single_gpu_config["task_submit_setting"]["llm_code_commit_id"] = self.commit_id
        odps_infer_single_gpu_config["task_submit_setting"]["project"] = "项目"
        odps_infer_single_gpu_config["task_submit_setting"]["scheduler_queue"] = "队列"
        odps_infer_single_gpu_config["task_submit_setting"]["world_size"] = "1"
        odps_infer_single_gpu_config["task_submit_setting"]["gpu_cores"] = "4"
        odps_infer_single_gpu_config["task_submit_setting"]["algo_name"] = "pytorch240"
        odps_infer_single_gpu_config["llm_running_param"]["basic"]["model_name_or_path"] = "xxx"
        odps_infer_single_gpu_config["llm_running_param"]["basic"]["template"] = "xxx"
        odps_infer_single_gpu_config["llm_running_param"]["basic"]["inputs"] = "xxx"
        odps_infer_single_gpu_config["llm_running_param"]["basic"]["outputs"] = "xxx"
        odps_infer_single_gpu_config["llm_running_param"]["basic"]["prompt_column"] = "xxx"
        odps_infer_single_gpu_config["llm_running_param"]["basic"]["image"] = "xxx"
        odps_infer_single_gpu_config["llm_running_param"]["extended"]["infer_mode"] = "vllm"
        odps_infer_single_gpu_config["llm_running_param"]["extended"]["flash_attn"] = "spda"

        task_manager = TaskManager(openlm_token=self.openlm_token)
        task_manager.run(task_config=odps_infer_single_gpu_config, access_id="xx", access_key="xxx")
        return task_manager
