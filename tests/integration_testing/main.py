import argparse
import os
import time
from typing import TYPE_CHECKING, List

import oss2
from oss2.credentials import EnvironmentVariableCredentialsProvider


if TYPE_CHECKING:
    from openlm_hub import TaskManager


def do_package():
    """
    调用方式，本地主要对要测试的代码git commit
    git commit之后，调用do_package,将测试的代码打包上传。
    相同commit 重复打包会报错。
    """
    import subprocess

    try:
        result = subprocess.run(["git", "rev-parse", "HEAD"], capture_output=True, text=True, check=True)
        long_commit_id = result.stdout.strip()
        short_commit_id = long_commit_id[:7]

    except subprocess.CalledProcessError as e:
        print(f"Error executing command: {e}")
        raise Exception("commit_id not ofund")
    zip_filename = f"TuningFactory-{short_commit_id}.zip"
    zip_dir = os.path.join("/tmp", "tuningfactory_test")
    os.makedirs(zip_dir, exist_ok=True)
    zip_path = os.path.join(zip_dir, zip_filename)
    try:
        subprocess.run(["zip", "-r", zip_path, ".", "-x", "*.git*"], check=True)
        print(f"Created zip file: {zip_path}")
    except subprocess.CalledProcessError as e:
        raise Exception(f"Error creating zip file: {e}")

    try:

        auth = oss2.ProviderAuthV4(EnvironmentVariableCredentialsProvider())

        endpoint = "https://oss-cn-zhangjiakou.aliyuncs.com"
        region = "cn-zhangjiakou"
        bucket = oss2.Bucket(auth, endpoint, "ados-nebula", region=region)
        bucket.put_object_from_file(f"nebula_internal/llm_code_test/{zip_filename}", zip_path)

        print("Upload successful.")

    except subprocess.CalledProcessError as e:
        print(f"Error uploading file: {e}")
        if "OSS_ACCESS_KEY_ID" not in os.environ or "OSS_ACCESS_KEY_SECRET" not in os.environ:
            print("Must set the ENV OSS_ACCESS_KEY_ID and OSS_ACCESS_KEY_SECRET to auth oss!")
        raise Exception(e.output)

    # 注册包
    import json

    import requests

    url = "https://nebula-service-center.alibaba-inc.com/openlm/v1/cli/internal/add_code"

    payload = json.dumps(
        {
            "code_path": f"oss-cn-zhangjiakou.aliyuncs.com/ados-nebula/nebula_internal/llm_code_test/{zip_filename}",
            "commit_id": long_commit_id,
            "git_address": "**************************:openlm/TuningFactory.git",
        }
    )
    headers = {
        "user-id": "174954",
        "Content-Type": "application/json",
        "Accept": "*/*",
        "Host": "nebula-service-center.alibaba-inc.com",
        "Connection": "keep-alive",
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    if response.ok:
        print(f"package success,commit id is {long_commit_id}")
    else:
        raise Exception(response.text)


def do_test(commitId):
    # test_sft
    # test_pt
    # test_lora
    # test_dpo/kto
    # test_infer
    tasks = []
    tasks.extend(do_infer(commitId))
    # 其他测试
    failed_tests = []
    finish_tests = []
    while True:
        for task in tasks:
            if task.is_running():
                pass
            else:
                finish_tests.append(task)
                if task.is_failed():
                    failed_tests.append((task.task_submit_result.task_id, task.task_submit_result.logview))
        if len(finish_tests) == len(tasks):
            break
        else:
            time.sleep(60)
    print(failed_tests)


def do_infer(commitId) -> List["TaskManager"]:
    task_list = []
    from test_infer import InferTest

    task_list.append(InferTest(commit_id=commitId, openlm_token="").test_llm_oss_infer_default())
    task_list.append(InferTest(commit_id=commitId, openlm_token="").test_llm_oss_infer_vllm())
    task_list.append(InferTest(commit_id=commitId, openlm_token="").test_mllm_oss_infer_default())
    task_list.append(InferTest(commit_id=commitId, openlm_token="").test_mllm_oss_infer_vllm())

    task_list.append(InferTest(commit_id=commitId, openlm_token="").test_llm_odps_infer_single_gpu())
    task_list.append(InferTest(commit_id=commitId, openlm_token="").test_llm_odps_infer_multi_gpu())
    task_list.append(InferTest(commit_id=commitId, openlm_token="").test_mllm_odps_infer_single_gpu())
    return task_list


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="打包测试工具")

    parser.add_argument("--action", type=str, required=True, help="package\test", choices=["package", "test"])
    parser.add_argument("--commitId", type=str, required=False, help="测试的commitId")
    args = parser.parse_args()
    if args.action == "package":
        do_package()
    else:
        do_test(args.commitId)
