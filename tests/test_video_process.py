from __future__ import annotations

import base64
import math
import warnings
from io import BytesIO
import json
import psutil
import os
import gc
import cv2

import requests
import torch
import numpy as np
from PIL import Image
from torchvision import io, transforms
from torchvision.transforms import InterpolationMode
from decord import VideoReader, cpu


IMAGE_FACTOR = 28
MIN_PIXELS = 4 * 28 * 28
MAX_PIXELS = 16384 * 28 * 28
MAX_RATIO = 200

VIDEO_MIN_PIXELS = 128 * 28 * 28
VIDEO_MAX_PIXELS = 768 * 28 * 28
VIDEO_TOTAL_PIXELS = 24576 * 28 * 28
FRAME_FACTOR = 2
FPS = 2.0
FPS_MIN_FRAMES = 4
FPS_MAX_FRAMES = 768


def round_by_factor(number: int, factor: int) -> int:
    """Returns the closest integer to 'number' that is divisible by 'factor'."""
    return round(number / factor) * factor


def ceil_by_factor(number: int, factor: int) -> int:
    """Returns the smallest integer greater than or equal to 'number' that is divisible by 'factor'."""
    return math.ceil(number / factor) * factor


def floor_by_factor(number: int, factor: int) -> int:
    """Returns the largest integer less than or equal to 'number' that is divisible by 'factor'."""
    return math.floor(number / factor) * factor


def smart_resize(
    height: int, width: int, factor: int = IMAGE_FACTOR, min_pixels: int = MIN_PIXELS, max_pixels: int = MAX_PIXELS
) -> tuple[int, int]:
    """
    Rescales the image so that the following conditions are met:

    1. Both dimensions (height and width) are divisible by 'factor'.

    2. The total number of pixels is within the range ['min_pixels', 'max_pixels'].

    3. The aspect ratio of the image is maintained as closely as possible.
    """
    if max(height, width) / min(height, width) > MAX_RATIO:
        raise ValueError(
            f"absolute aspect ratio must be smaller than {MAX_RATIO}, got {max(height, width) / min(height, width)}"
        )
    h_bar = max(factor, round_by_factor(height, factor))
    w_bar = max(factor, round_by_factor(width, factor))
    if h_bar * w_bar > max_pixels:
        beta = math.sqrt((height * width) / max_pixels)
        h_bar = floor_by_factor(height / beta, factor)
        w_bar = floor_by_factor(width / beta, factor)
    elif h_bar * w_bar < min_pixels:
        beta = math.sqrt(min_pixels / (height * width))
        h_bar = ceil_by_factor(height * beta, factor)
        w_bar = ceil_by_factor(width * beta, factor)
    return h_bar, w_bar


def fetch_image(ele: dict[str, str | Image.Image], size_factor: int = IMAGE_FACTOR) -> Image.Image:
    if "image" in ele:
        image = ele["image"]
    else:
        image = ele["image_url"]
    image_obj = None
    if isinstance(image, Image.Image):
        image_obj = image
    elif image.startswith("http://") or image.startswith("https://"):
        image_obj = Image.open(requests.get(image, stream=True).raw)
    elif image.startswith("file://"):
        image_obj = Image.open(image[7:])
    elif image.startswith("data:image"):
        data = image.split(";", 1)[1]
        if data.startswith("base64,"):
            data = base64.b64decode(data[7:])
            image_obj = Image.open(BytesIO(data))
    else:
        image_obj = Image.open(image)
    if image_obj is None:
        raise ValueError(f"Unrecognized image input, support local path, http url, base64 and PIL.Image, got {image}")
    image = image_obj.convert("RGB")
    ## resize
    if "resized_height" in ele and "resized_width" in ele:
        resized_height, resized_width = smart_resize(
            ele["resized_height"],
            ele["resized_width"],
            factor=size_factor,
        )
    else:
        width, height = image.size
        min_pixels = ele.get("min_pixels", MIN_PIXELS)
        max_pixels = ele.get("max_pixels", MAX_PIXELS)
        resized_height, resized_width = smart_resize(
            height,
            width,
            factor=size_factor,
            min_pixels=min_pixels,
            max_pixels=max_pixels,
        )
    image = image.resize((resized_width, resized_height))

    return image

def read_video_with_cv2(video_path, start_pts=0.0, end_pts=None, fps=None):
    cap = cv2.VideoCapture(video_path)

    # 获取视频的一些基本信息
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    video_fps = cap.get(cv2.CAP_PROP_FPS)

    # 如果未指定结束时间，则默认为视频时长
    if end_pts is None:
        end_pts = frame_count / video_fps  # 结束时间为总帧数 / FPS

    # 计算要读取的起始和结束帧
    start_frame = int(start_pts * video_fps)
    end_frame = int(end_pts * video_fps)

    # 确保开始和结束帧在合法范围内
    start_frame = max(0, start_frame)
    end_frame = min(frame_count, end_frame)

    frames = []

    # 跳到起始帧
    cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)

    for frame_idx in range(start_frame, end_frame):
        ret, frame = cap.read()
        if not ret:
            break

        # 转换 BGR 到 RGB，转换到 PyTorch 张量，并调整为 CHW 格式
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        frame_tensor = torch.from_numpy(frame).permute(2, 0, 1)  # HWC to CHW
        frames.append(frame_tensor)

    cap.release()

    # 将列表转换为 tensor，调整为 TCHW 格式
    video_tensor = torch.stack(frames)  # 现在是 (T, C, H, W)

    # 返回视频张量、音频（None，未处理）、视频相关信息
    return video_tensor, None, {"video_fps": video_fps, "height": height, "width": width}


def fetch_video(ele: dict, size_factor: int = FRAME_FACTOR, read_format = 'torchvision') -> torch.Tensor | list[Image.Image]:
    if isinstance(ele["video"], str):
        # TODO: support http url

        video = ele["video"]
        if video.startswith("file://"):
            video = video[7:]

        if read_format == 'torchvision':
            video, audio, info = io.read_video(
                video,
                start_pts=ele.get("video_start", 0.0),
                end_pts=ele.get("video_end", None),
                pts_unit="sec",
                output_format="TCHW",
            )
        else:
            video, audio, info = read_video_with_cv2(video, start_pts=ele.get("video_start", 0.0), end_pts=ele.get("video_end", None))

        assert not ("fps" in ele and "nframes" in ele), "Only accept either `fps` or `nframes`"
        if "nframes" in ele:
            nframes = round_by_factor(ele["nframes"], size_factor)
        else:
            fps = ele.get("fps", FPS)
            # print(f'video size: {video.size()}')
            nframes = video.size(0) / info["video_fps"] * fps
            nframes = round_by_factor(nframes, size_factor)
            if "min_frames" in ele:
                min_frames = ele["min_frames"]
                if nframes < min_frames:
                    nframes = ceil_by_factor(min_frames, size_factor)
            else:
                min_frames = FPS_MIN_FRAMES
                if nframes < min_frames:
                    warnings.warn(f"nframes is less than DEFAULT_MIN_FRAMES {min_frames}, set to {nframes}.")
                    nframes = ceil_by_factor(min_frames, size_factor)
            if "max_frames" in ele:
                max_frames = ele["max_frames"]
                if nframes > max_frames:
                    nframes = floor_by_factor(max_frames, size_factor)
            else:
                max_frames = FPS_MAX_FRAMES
                if nframes > max_frames:
                    warnings.warn(f"nframes is greater than DEFAULT_MAX_FRAMES {max_frames}, set to {nframes}.")
                    nframes = floor_by_factor(max_frames, size_factor)

        if not (size_factor <= nframes and nframes <= video.size(0)):
            raise ValueError(f"nframes should in interval [{size_factor}, {video.size(0)}], but got {nframes}.")

        idx = torch.linspace(0, video.size(0) - 1, nframes).round().long()
        height, width = video.shape[2:]
        # print(f'height: {height}, width: {width}')
        video = video[idx]
        # print(f'src video: {video}')

        min_pixels = ele.get("min_pixels", VIDEO_MIN_PIXELS)
        total_pixels = ele.get("total_pixels", VIDEO_TOTAL_PIXELS)
        max_pixels = max(min(VIDEO_MAX_PIXELS, total_pixels / nframes * size_factor), min_pixels * 1.05)
        max_pixels = ele.get("max_pixels", max_pixels)
        if "resized_height" in ele and "resized_width" in ele:
            resized_height, resized_width = smart_resize(
                ele["resized_height"],
                ele["resized_width"],
                factor=size_factor,
            )
        else:
            resized_height, resized_width = smart_resize(
                height,
                width,
                factor=size_factor,
                min_pixels=min_pixels,
                max_pixels=max_pixels,
            )
        # print(f'fetch_video: video type {type(video)} ')
        video = transforms.functional.resize(
            video,
            [resized_height, resized_width],
            interpolation=InterpolationMode.BICUBIC,
            antialias=True,
        ).float()
        return video
    else:
        assert isinstance(ele["video"], (list, tuple))
        process_info = ele.copy()
        process_info.pop("type", None)
        process_info.pop("video", None)
        images = [fetch_image({"image": video_element, **process_info}) for video_element in ele["video"]]
        nframes = ceil_by_factor(len(images), size_factor)
        if len(images) < nframes:
            images.extend([images[-1]] * (nframes - len(images)))
        return images


def extract_vision_info(conversations: list[dict] | list[list[dict]]) -> list[dict]:
    vision_infos = []
    if isinstance(conversations[0], dict):
        conversations = [conversations]
    for conversation in conversations:
        for message in conversation:
            if isinstance(message["content"], list):
                for ele in message["content"]:
                    if (
                        "image" in ele
                        or "image_url" in ele
                        or "video" in ele
                        or ele["type"] in ("image", "image_url", "video")
                    ):
                        vision_infos.append(ele)
    return vision_infos


def process_vision_info(
    conversations: list[dict] | list[list[dict]],
) -> tuple[list[Image.Image] | None, list[torch.Tensor | list[Image.Image]] | None]:
    vision_infos = extract_vision_info(conversations)
    ## Read images or videos
    image_inputs = []
    video_inputs = []
    for vision_info in vision_infos:
        if "image" in vision_info or "image_url" in vision_info:
            image_inputs.append(fetch_image(vision_info))
        elif "video" in vision_info:
            video_inputs.append(fetch_video(vision_info))
        else:
            raise ValueError("image, image_url or video should in content.")
    if len(image_inputs) == 0:
        image_inputs = None
    if len(video_inputs) == 0:
        video_inputs = None
    return image_inputs, video_inputs


def new_fetch_video(video_path):
    video = VideoReader(video_path, ctx=cpu(0), num_threads=1)
    size_factor = FRAME_FACTOR
    video_fps = len(video)
    avg_fps = video.get_avg_fps()
    nframes = video_fps / avg_fps * FPS
    nframes = round_by_factor(nframes, size_factor)
    min_frames = FPS_MIN_FRAMES
    if nframes < min_frames:
        warnings.warn(f"nframes is less than DEFAULT_MIN_FRAMES {min_frames}, set to {nframes}.")
        nframes = self._ceil_by_factor(min_frames, size_factor)

    max_frames = FPS_MAX_FRAMES
    if nframes > max_frames:
        warnings.warn(f"nframes is greater than DEFAULT_MAX_FRAMES {max_frames}, set to {nframes}.")
        nframes = floor_by_factor(max_frames, size_factor)

    if not (size_factor <= nframes and nframes <= video_fps):
        raise ValueError(f"nframes should in interval [{size_factor}, {video_fps}], but got {nframes}.")

    idx = torch.linspace(0, video_fps - 1, nframes).round().long()
    # print(f'new geatch video: {idx}')
    video = video.get_batch(idx).asnumpy()
    # print(f'new video: {video}')
    video = torch.from_numpy(video)
    video = video.permute(0, 3, 1, 2)

    min_pixels = VIDEO_MIN_PIXELS
    total_pixels = VIDEO_TOTAL_PIXELS
    max_pixels = max(min(VIDEO_MAX_PIXELS, total_pixels / nframes * size_factor), min_pixels * 1.05)

    height, width = video.shape[2:]
    # print(f'height: {height}, width: {width}')
    resized_height, resized_width = smart_resize(
        height,
        width,
        factor=size_factor,
        min_pixels=min_pixels,
        max_pixels=max_pixels,
    )
    video = transforms.functional.resize(
        video,
        [resized_height, resized_width],
        interpolation=InterpolationMode.BICUBIC,
        antialias=True,
    ).float()
    return video




if __name__ == '__main__':
    # profile memory
    # video_file = open('data/gpt4o.jsonl')
    # for i, element in enumerate(video_file.readlines()):
    #     element = json.loads(element)
    #     video = element['video']
    #     video = f'/data/oss_bucket_0/datasets/ShareGPT-4o/videos/{video}'
    #     if not os.path.exists(video):
    #         continue
    #     src_videos = fetch_video({'video': video})
    #     gc.collect()
    #     mem = psutil.virtual_memory()    
    #     print(f'{i:8} - {mem.percent:18} - {mem.used/1024**3:10.2f} - {mem.free/1024**3:10.2f} - {mem.available/1024**3:10.2f}')


    # compare video result
    torchvision_videos = fetch_video({'video': 'data/cute_baby.mp4'}, read_format='torchvision')
    cv2_videos = fetch_video({'video': 'data/cute_baby.mp4'}, read_format='cv2')
    print(torch.all(torchvision_videos == cv2_videos))