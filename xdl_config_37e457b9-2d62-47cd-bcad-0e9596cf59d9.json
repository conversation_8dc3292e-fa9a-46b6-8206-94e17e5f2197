{"job_name": "/data/xpfs_0/data/model/Seed-Coder-8B-Base/-2025-05-14-09-07-14", "training_type": "pytorch", "docker_image": "reg.docker.alibaba-inc.com/alimama/xdl:pytorch_alpha", "script": "", "worker": {"instance_num": 112, "cpu_cores": 8.0, "gpu_cores": 1.0, "memory_m": 80000}, "ps": {"instance_num": 0, "cpu_cores": 4, "gpu_cores": 1, "memory_m": 40000}, "reader": {"data_dir": "./data"}, "checkpoint": {"output_dir": "./ckpt"}, "meta_dir": "./meta", "parameter": {"worker": {"user_define_cmd": "src/train_bash.py --stage pt     --model_name_or_path=/data/xpfs_0/data/model/Seed-Coder-8B-Base/     --do_train     --do_eval      --val_size=0.005     --eval_steps 50     --dataset=pt-taotian-8k    --prompt=text     --finetuning_type full     --output_dir=/data/xpfs_0/data/model/llama3-8b-fim_0512/     --overwrite_cache     --per_device_eval_batch_size 1     --per_device_train_batch_size 4     --gradient_accumulation_steps 1     --lr_scheduler_type cosine     --logging_steps 10     --save_steps 500     --num_train_epochs 1     --learning_rate=1e-5     --warmup_ratio 0.1     --cutoff_len=8192     --packing False     --preprocessing_num_workers=64     --dataloader_num_workers=4     --plot_loss     --do_sample     --deepspeed=scripts/ds_zero3.json     --bf16"}}, "min_finish_worker_rate": 100, "oss_access_id": "LTAIGcY7idsIs0lm", "oss_access_key": "KM7z2PawYxDLqZWIFk7VraBUlZyBLk", "oss_bucket": "mtl4-ai-resources-daily", "job_success_notice": "true", "oss_endpoint": "oss-cn-hangzhou-internal.aliyuncs.com", "algo_name": "pytorch220", "scheduler_queue": "taobao_dev_mtl"}