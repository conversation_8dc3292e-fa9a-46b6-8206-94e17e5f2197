#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移除字符串中 <think> 标签及其内容的示例
"""

import re
from typing import List


def remove_think_tags_regex(text: str) -> str:
    """
    使用正则表达式移除 <think> 标签及其内容
    支持多行内容和嵌套情况
    """
    # 使用非贪婪匹配，支持多行模式
    pattern = r'<think>.*?</think>'
    result = re.sub(pattern, '', text, flags=re.DOTALL)
    return result.strip()


def remove_think_tags_regex_multiline(text: str) -> str:
    """
    使用正则表达式移除多个 <think> 标签及其内容
    """
    # 全局匹配所有 think 标签
    pattern = r'<think>.*?</think>'
    result = re.sub(pattern, '', text, flags=re.DOTALL)
    # 清理多余的空行
    result = re.sub(r'\n\s*\n', '\n', result)
    return result.strip()


def remove_think_tags_manual(text: str) -> str:
    """
    手动查找和移除 think 标签（处理简单情况）
    """
    result = text
    while '<think>' in result and '</think>' in result:
        start = result.find('<think>')
        end = result.find('</think>') + len('</think>')
        if start != -1 and end != -1 and start < end:
            result = result[:start] + result[end:]
        else:
            break
    return result.strip()


def remove_think_tags_advanced(text: str) -> str:
    """
    高级版本：处理嵌套标签和不完整标签
    """
    import re
    
    # 处理嵌套的 think 标签
    pattern = r'<think>(?:[^<]|<(?!/think>))*</think>'
    
    # 循环处理，直到没有更多的 think 标签
    while re.search(pattern, text, re.DOTALL):
        text = re.sub(pattern, '', text, flags=re.DOTALL)
    
    # 清理多余的空白
    text = re.sub(r'\n\s*\n+', '\n\n', text)
    return text.strip()


def clean_whitespace(text: str) -> str:
    """
    清理移除标签后的多余空白
    """
    # 移除多余的空行
    text = re.sub(r'\n\s*\n+', '\n\n', text)
    # 移除行首行尾空白
    lines = [line.strip() for line in text.split('\n')]
    # 过滤空行
    lines = [line for line in lines if line]
    return '\n'.join(lines)


# 测试示例
def test_examples():
    """测试不同情况的示例"""
    
    # 示例1：简单情况
    text1 = """这是一段文本。
<think>
这是需要移除的思考内容
包含多行
</think>
这是保留的内容。"""
    
    print("示例1 - 简单情况:")
    print("原文本:")
    print(text1)
    print("\n移除后:")
    print(remove_think_tags_regex(text1))
    print("\n" + "="*50 + "\n")
    
    # 示例2：多个标签
    text2 = """开始内容
<think>第一个思考</think>
中间内容
<think>
第二个思考
多行内容
</think>
结束内容"""
    
    print("示例2 - 多个标签:")
    print("原文本:")
    print(text2)
    print("\n移除后:")
    print(remove_think_tags_regex_multiline(text2))
    print("\n" + "="*50 + "\n")
    
    # 示例3：包含其他XML标签的情况
    text3 = """<response>
这是回答内容
<think>这是思考过程，需要移除</think>
<example>这是示例，需要保留</example>
</response>"""
    
    print("示例3 - 混合XML标签:")
    print("原文本:")
    print(text3)
    print("\n移除后:")
    print(remove_think_tags_regex(text3))
    print("\n" + "="*50 + "\n")


if __name__ == "__main__":
    # 运行测试示例
    test_examples()
    
    # 交互式测试
    print("交互式测试 (输入 'quit' 退出):")
    while True:
        user_input = input("\n请输入包含 <think> 标签的文本: ")
        if user_input.lower() == 'quit':
            break
        
        result = remove_think_tags_regex_multiline(user_input)
        print("移除结果:")
        print(result)
