# Training LLM on Nebula

这是一份在 nebula 上训练 LLM 的使用指南。

# ！！如果有基于这个仓库进行二次开发或者业务应用，请在业务侧注明引用

(初始代码来自 [LLaMA Factory](https://github.com/hiyouga/LLaMA-Factory/commit/6bf4c1274f4e9c91a6b37bd4c420c2369eaae65a)，原 README 见 [中文](README_zh.md)/[English](README_en.md))


## 快速开始

[QuickStart](https://aliyuque.antfin.com/uxctvg/yd0939/kstt6al1u23qfp6k)

## 数据集

更详细的数据集配置可见文档：[TuningFactory 数据集配置](https://aliyuque.antfin.com/mdl/llm/qaorgu7fp1mhs8p1)

## 训练

详细文档可见：
- SFT 训练：[TuningFactory SFT 训练](https://aliyuque.antfin.com/alimama-nebula/vbw69h/lkbzp9b3su2glaep)
- RM 训练：[TuningFactory RM 训练](https://aliyuque.antfin.com/alimama-nebula/vbw69h/cgpri57fe83ua7cg)
- DPO 训练：[TuningFactory DPO 训练](https://aliyuque.antfin.com/alimama-nebula/vbw69h/dfcphychegzcuvnu)
- PPO 训练：[TuningFactory PPO 训练](https://aliyuque.antfin.com/alimama-nebula/vbw69h/nr49wkl4d706huci)

支持的训练参数、模型与训练方式等请参照原 [README](README_zh.md)。

详细参数说明见：[TuningFactory 参数说明](https://aliyuque.antfin.com/alimama-nebula/vbw69h/qaorgu7fp1mhs8p1#i9bpC)

各种训练方式的示例：
```sh
# 全参数 sft 训练
sh scripts/sft_ds.sh
# lora sft 训练
sh scripts/sft_lora.sh
# lora reward model 训练
sh scripts/rm_lora.sh
# 全参数 reward model 训练
sh scripts/rm_ds.sh
# lora reward model 评估
sh scripts/rm_eval_lora.sh
# 全参数 reward model 模型评估
sh scripts/rm_eval_ds.sh
# lora ppo 训练
sh scripts/ppo_lora.sh
# 全参数 ppo 训练
sh scripts/ppo_ds.sh
# lora dpo 训练
sh scripts/dpo_lora.sh
# 全参数 dpo 训练
sh scripts/dpo_ds.sh
```

以上脚本仅为示例，请自行配置和调整运行脚本和 `config.sh` 中的参数。

## 离线推理

详细介绍可见：[TuningFactory 离线推理](https://aliyuque.antfin.com/alimama-nebula/vbw69h/ih5pqtnhx5vdfl82)

离线推理支持多机ODPS数据源: [EIP](https://aliyuque.antfin.com/uxctvg/gh8c24/lkr4c8) 和 单机本地数据集两种方式。

推理脚本： [scripts/inference.sh](scripts/inference.sh)，详细配置方式见脚本内注释。

## 联系我们

如果在使用过程中遇到问题，请联系我们：
- “MDL训练平台用户群”群的钉钉群号： 35727777

![用户群](assets/mdl-ding.png)
